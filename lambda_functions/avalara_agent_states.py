import utilities
import assembled_agent_states
import avalara_people as avalara_people
from slack_util import send_slack_error
from logger_util import get_logger

logger = get_logger(__name__)


def get_state(agent_state_name):
    return agent_state_name


def update_presence_states(agent_presence_records):
    """
    Fetch and match agent presence records, check platform IDs, and update states in Assembled.
    """

    # Loop through agent presence records
    for agent_record in agent_presence_records:
        agent_id = agent_record.get("agentId")
        agent_state_name = agent_record.get("agentStateName")
        agent_state_name = get_state(agent_state_name)

        logger.info(f"Processing sys_id: {agent_id}, State: {agent_state_name}")

        # Fetch user from source
        source_data = avalara_people.get_person(agent_id)
        if source_data.get("error") is not None:
            error_msg = source_data["error"]
            logger.error(error_msg)
            # send_slack_error(error_msg)
            continue

        updated_at = agent_record.get("lastUpdateTime")
        epoch_time = utilities.get_epoch_time(updated_at)

        source_data["source_state"] = agent_state_name
        source_data["source_state_time"] = epoch_time

        # Check and update agent in Assembled
        result = assembled_agent_states.update_agent_state(source_data)
        logger.debug(result)