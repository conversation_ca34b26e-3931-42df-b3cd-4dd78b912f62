import assembled
import assembled_people
from logger_util import get_logger
from slack_util import send_slack_error

logger = get_logger(__name__)

def update_agent_state(source_data):
    # Verify that the agent is set up in Assembled
    assembled_agent = assembled_people.check_and_update_agent(source_data)
    if assembled_agent is None:
        error_msg = f"Agent cannot be found or created - cannot import agent state"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return None

    assembled_agent_api_id = assembled_agent.get("platforms", {}).get("api")
    if assembled_agent_api_id is None:
        error_msg = f"Agent platforms.api is empty - cannot import agent state"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return None    

    # Update the agent state
    source_id = source_data.get("source_agent_id")
    source_state = assembled.get_state(source_data.get("source_state"))
    time = source_data.get("source_state_time")
    state = {
        "agent_platform_id": source_id,
        "time": time,
        "state": source_state
    }

    try:
        state_response = assembled.post_agent_state(state)
        return state_response
    except Exception as e:
        error_msg = f"Failed to post agent state to Assembled: {str(e)}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return None

