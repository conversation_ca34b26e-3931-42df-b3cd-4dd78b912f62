import assembled
from slack_util import send_slack_error
from logger_util import get_logger

logger = get_logger(__name__)

def post_conversations(conversations):
    try:
        post_conversations_response = assembled.post_conversations(conversations)
        return post_conversations_response
    except Exception as e:
        error_msg = f"Failed to post conversations to Assembled: {str(e)}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return None