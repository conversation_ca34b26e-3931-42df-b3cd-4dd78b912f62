import servicenow
import assembled
import utilities
from draft_kings_conversations import get_channel
from logger_util import get_logger

logger = get_logger(__name__)

def sync_handle_times(from_date):
    errors = 0
    processed = 0
    time_worked_items = servicenow.get_time_worked(from_date)
    
    for item in time_worked_items:
        logger.info(f"Processing handle time: {item.get('sys_id')}")
    
    handle_times = [{
        "agent_platform_id": item.get("user", {}).get("value"),
        "ticket_platform_id": item.get("document", {}).get("value"),
        "start_time": utilities.get_epoch_time(item["start_date_time"]),
        "end_time": utilities.get_epoch_time(item["end_date_time"]),
        "channel": get_channel(item.get("contact_type", "")),
        "sys_id": item.get("sys_id", "")
    } for item in time_worked_items]
    
    try:
        r = assembled.post_handle_times(handle_times)
        if r is None:
            logger.error(f"Error posting handle times: unknown error")
            errors += len(handle_times)
        else:
            processed += len(handle_times)
    except Exception as e:
        logger.error(f"Error posting handle times: {str(e)}")
        errors += len(handle_times)

    return {"errors": errors, "processed": processed }
