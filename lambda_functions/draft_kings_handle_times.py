import servicenow
import assembled
import utilities
from draft_kings_conversations import get_channel
from logger_util import get_logger

logger = get_logger(__name__)

def sync_handle_times(from_date):
    errors = 0
    processed = 0
    youngest = from_date
    time_worked_items = servicenow.get_time_worked(from_date)
    
    for item in time_worked_items:
        if "sys_updated_on" in item and item["sys_updated_on"] > youngest:
            youngest = item["sys_updated_on"]
    
    handle_times = [{
        "agent_platform_id": item.get("user", {}).get("value"),
        "ticket_platform_id": item.get("document", {}).get("value"),
        "start_time": utilities.get_epoch_time(item["start_date_time"]),
        "end_time": utilities.get_epoch_time(item["end_date_time"]),
        "channel": get_channel(item.get("contact_type", ""))
    } for item in time_worked_items if (item.get("document", {}).get("value") is not None) and (item.get("user", {}).get("value") is not None)]
    
    excluded = [item.get("user", {}).get("value") for item in time_worked_items if (item.get("document", {}).get("value") is not None) and (item.get("user", {}).get("value") is not None)]
    
    logger.info(f"Excluded {len(excluded)}/{len(time_worked_items) + len(excluded)} handle times")
    
    for user_id in excluded:
        logger.info(f"Excluded handle time: {user_id}")
    
    for i in range(0, len(handle_times), 1000):
        batch = handle_times[i:i + 1000]
        try:
            r = assembled.post_handle_times(batch)
            if r is None:
                logger.error(f"Error posting handle times: unknown error")
                errors += len(batch)
            else:
                processed += len(batch)
        except Exception as e:
            logger.error(f"Error posting handle times: {str(e)}")
            errors += len(batch)

    return {"errors": errors, "processed": processed, "youngest": youngest}
