import datetime
import json
import draft_kings_agent_states
import draft_kings_handle_times
import servicenow
from logger_util import get_logger

logger = get_logger(__name__)

def handler(event, context):
    try:
        logger.info("Fetching and matching records from ServiceNow and updating Assembled...")

        # Calculate startDateTime as 15 minutes before current execution time
        current_time = datetime.datetime.utcnow()
        startDateTime = (current_time - datetime.timedelta(seconds=900)).strftime(
            "%Y-%m-%d %H:%M:%S"
        )

        endpoint_filter = f"?sysparm_query=sys_updated_on>{startDateTime}&sysparm_order_by=sys_updated_on"

        # Fetch SNOW presence state records and build map
        presence_state_records = servicenow.get_agent_presence_states()
        if not presence_state_records:
            error_msg = "Failed to fetch presence state records from ServiceNow"
            logger.error(error_msg)
            # send_slack_error(error_msg)``
            return {
                "statusCode": 500,
                "body": json.dumps({"error": error_msg})
            }

        presence_state_map = {
            record["sys_id"]: record["name"] for record in presence_state_records
        }

        # Agent States
        agent_presence_records = servicenow.get_agent_presence_records(
            endpoint_filter
        )

        update_result = draft_kings_agent_states.update_presence_states(presence_state_map, agent_presence_records)
        handle_times_result = draft_kings_handle_times.sync_handle_times(startDateTime)

        return {
            "statusCode": 200,
            "body": json.dumps({
                "message": "Agent states and handle times updated successfully",
                "result": [update_result, handle_times_result],
            })
        }
    except Exception as e:
        error_msg = f"Unexpected error in agent states handler: {str(e)}"
        logger.error(error_msg, exc_info=True)
        # send_slack_error(error_msg)
        return {
            "statusCode": 500,
            "body": json.dumps({"error": error_msg})
        }
