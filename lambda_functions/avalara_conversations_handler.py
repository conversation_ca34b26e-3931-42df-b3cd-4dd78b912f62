import requests
import nice_incontact
import avalara_conversations
import assembled_conversations
from datetime import datetime, timed<PERSON><PERSON>


def handler(event, context):
    try:
        token_response = nice_incontact.get_token()
        if token_response is None:
            return {
                "statusCode": 400,
                "body": "Failed to obtain token"
            }

        # Get contacts (conversations)
        last_run = (datetime.now() - timedelta(seconds=15)).strftime("%Y-%m-%d %H:%M:%S")
        contacts = nice_incontact.get_contacts(last_run)
        conversations = avalara_conversations.get_conversations(contacts)
        post_cases_response = assembled_conversations.post_conversations(conversations)

        return {
            "statusCode": 200,
            "body": {
                "message": "Conversations processed successfully",
                "result": post_cases_response
            }
        }

    except requests.exceptions.RequestException as e:
        return {
            "statusCode": 500,
            "body": f"An error occurred with the API request: {str(e)}"
        } 