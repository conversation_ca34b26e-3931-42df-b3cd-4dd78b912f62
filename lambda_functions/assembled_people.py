import assembled
import utilities
import dynamodb_util
from slack_util import send_slack_error
from logger_util import get_logger

logger = get_logger(__name__)


def check_and_update_agent(source_data):
    """
    Check if agent exists in Assembled with imported_id and platforms.api. If not, create and/or update person.
    """

    # Check if agent with matching email exists
    assembled_agent = None
    source_agent_active = source_data.get('source_agent_active')
    source_agent_id = source_data.get("source_agent_id")
    source_agent_email = source_data.get("source_agent_email")
    source_agent_name = source_data.get("source_agent_name")
    source_agent_first_name = source_data.get("source_agent_first_name")
    source_agent_last_name = source_data.get("source_agent_last_name")
    source_agent_start_date = source_data.get("source_agent_start_date")
    source_agent_end_date = source_data.get("source_agent_end_date")
    source_agent_role = source_data.get("source_agent_role", "527c3244-b80b-435e-82ed-6f740714847d")
    source_agent_teams = source_data.get("source_agent_teams", ["2cf5692e-7a48-4f88-96f8-d16b679082ee"])
    source_agent_time_zone = source_data.get("source_agent_time_zone", "America/New_York")
    source_agent_staffable = source_data.get("source_agent_staffable", True)
    source_agent_channels = source_data.get("source_agent_channels", ["email"])
    source_agent_queues = source_data.get("source_agent_queues", [])

    if source_agent_active is None or source_agent_active == "false":
        logger.info(f"Agent {source_agent_id} is not active - cannot create person")
        return assembled_agent

    assembled_email = source_agent_email
    sandbox = False
    if sandbox:
        assembled_email = utilities.modify_email(source_agent_email, "sandbox")

    cached_agent = dynamodb_util.get_cached_assembled_person(assembled_email)
    if cached_agent:
        logger.info(f"Found cached Assembled person for email: {assembled_email}")
        return cached_agent

    people = assembled.get_people_by_email(assembled_email)
    agent_exists = people is not None

    if agent_exists:
        agent_exists = any(person.get("email") == assembled_email for person in people)

    # Associate or create Assembled People record
    if agent_exists:
        assembled_agent = people[0]
        assembled_people_id = assembled_agent.get("id")
        assembled_agent_id = assembled_agent.get("agent_id")
        assembled_agent_imported_id = assembled_agent.get("imported_id")
        assembled_agent_api_id = assembled_agent.get("platforms", {}).get("api")
        assembled_agent_staffable = assembled_agent.get("staffable")

        # Add the imported_id and platforms.api value, if necessary
        
        
        
        if not assembled_agent_imported_id or not assembled_agent_api_id:
            people_data = {
                "imported_id": source_agent_id,
                "platforms": {"api": source_agent_id}
            }
            
            people_patch_response = assembled.update_person(assembled_people_id, people_data)
            error = people_patch_response.get("error", "")
            if error:
                error_msg = f"Failed to add imported_id and platforms.api for user: {assembled_email}. Error: {error}"
                logger.error(error_msg)
                # send_slack_error(error_msg)
                return {"error": "Failed to add imported_id and platforms.api"}

            assembled_agent = people_patch_response["people"][0]

            # Create the platforms.api association
            if not assembled_agent_id:
                assembled_agent = people_patch_response["people"][0]
                assembled_agent_id = assembled_agent.get("agent_id")

            if not assembled_agent_id:
                error_msg = f"agent_id for person {assembled_people_id} is not present"
                logger.error(error_msg)
                # send_slack_error(error_msg)
                return {"error": error_msg}

            assembled_agent_api_id = assembled_agent.get("platforms", {}).get("api")
            if assembled_agent_staffable and assembled_agent_id and assembled_agent_api_id:
                platforms_api_data = {
                    "agent_id": assembled_agent_id,
                    "agent_platform_id": source_agent_id
                }

                agent_association_response = assembled.create_agent_association(platforms_api_data)
                if not agent_association_response:
                    error_msg = f"Failed to create agent_association for {assembled_agent_id}"
                    logger.error(error_msg)
                    # send_slack_error(error_msg)
                    return {"error": error_msg}
    else:
        # Create assembled person record
        source_agent_id = source_data.get("source_agent_id")
        people_data = {
            "email": assembled_email,
            "name": source_agent_name,
            "first_name": source_agent_first_name,
            "last_name": source_agent_last_name,
            "role": source_agent_role,
            "teams": source_agent_teams,
            "timezone": source_agent_time_zone,
            "staffable": source_agent_staffable,
            "imported_id": source_agent_id,
            "platforms": {"api": source_agent_id},
            "channels": source_agent_channels,
            "send_invite_email": False,
            "start_date": source_agent_start_date,
            "end_date": source_agent_end_date
        }

        # Only add queues if they exist
        if source_agent_queues:
            people_data["queues"] = source_agent_queues

        created_assembled_agent = assembled.create_person(people_data)
        if created_assembled_agent.get("error") is None:
            assembled_agent = created_assembled_agent

    # Cache the result if we have a valid agent
    if assembled_agent and not assembled_agent.get("error"):
        dynamodb_util.cache_assembled_person(assembled_email, assembled_agent)

    return assembled_agent