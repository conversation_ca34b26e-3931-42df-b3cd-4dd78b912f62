import servicenow
import utilities
import dynamodb_util
from logger_util import get_logger
from slack_util import send_slack_error

logger = get_logger(__name__)


def get_agent(agent_id):
    cached_agent = dynamodb_util.get_cached_draft_kings_agent(agent_id)
    if cached_agent:
        logger.info(f"Found cached Draft Kings agent for ID: {agent_id}")
        return cached_agent

    filter = f"/{agent_id}"
    if "@" in agent_id:
        filter = f"?sysparm_query=email={agent_id}"

    snow_agent_response = servicenow.get_user(filter)

    if not snow_agent_response or not isinstance(snow_agent_response, dict):
        error_msg = f"Unexpected or empty response for agent ID: {agent_id}. Response: {snow_agent_response}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return {"error": f"Failed to retrieve agent {agent_id}"}

    if snow_agent_response and not snow_agent_response.get("error"):
        dynamodb_util.cache_draft_kings_agent(agent_id, snow_agent_response)

    return snow_agent_response


def get_person(agent_id):
    # Fetch user from ServiceNow
    snow_agent_response = get_agent(agent_id)
    if snow_agent_response.get("error"):
        error_msg = f"Unexpected or empty response for agent ID: {agent_id}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return {"error": snow_agent_response["error"]}

    # Extract email and name fieldsmi
    agent_active = snow_agent_response["active"]
    # if agent_active is False or agent_active == "false":
    #     error_msg = f"Agent {agent_id} not active"
    #     logger.error(error_msg)
    #     # send_slack_error(error_msg)
    #     return {"error": error_msg}

    agent_email = snow_agent_response.get("email")
    agent_name = snow_agent_response.get("name")
    agent_first_name = snow_agent_response.get("first_name")
    agent_last_name = snow_agent_response.get("last_name")
    agent_start_date = utilities.get_epoch_time(snow_agent_response.get("sys_created_on"))
    agent_end_date = None
    agent_active = snow_agent_response.get("active")
    if not agent_active:
        agent_end_date = utilities.get_epoch_time(snow_agent_response.get("sys_updated_on"))

    # if not agent_email or not agent_name:
    #     error_msg = f"Email or name missing for agent ID. Email: {agent_email}, Name: {agent_name}"
    #     logger.error(error_msg)
    #     # send_slack_error(error_msg)
    #     return {"error": f"Failed to retrieve agent {agent_id}"}

    # Prepare Assembled data
    snow_data = {
        "source_agent_active": agent_active,
        "source_agent_id": agent_id,
        "source_agent_email": agent_email,
        "source_agent_first_name": agent_first_name,
        "source_agent_last_name": agent_last_name,
        "source_agent_name": agent_name,
        "source_agent_start_date": agent_start_date,
        "source_agent_end_date": agent_end_date
    }

    return snow_data