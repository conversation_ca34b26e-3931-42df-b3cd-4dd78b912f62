import os
import json
import boto3
from logger_util import get_logger
from dotenv import load_dotenv
from pathlib import Path
from slack_util import send_slack_error

logger = get_logger(__name__)


class SecretsUtil:
    _secrets = None

    @classmethod
    def get_secrets(cls):
        if cls._secrets is not None:
            return cls._secrets

        # Load environment variables from .env file in project root
        env_path = Path(__file__).resolve().parent.parent / '.env'
        load_dotenv(dotenv_path=env_path)

        # Check if we're running locally
        if os.getenv("LOCAL_DEVELOPMENT"):
            # Use environment variables for local development
            cls._secrets = {
                "ASSEMBLED_API_BASE_URL": os.getenv("ASSEMBLED_API_BASE_URL"),
                "ASSEMBLED_API_KEY": os.getenv("ASSEMBLED_API_KEY"),
                "SNOW_SUBDOMAIN": os.getenv("SNOW_SUBDOMAIN"),
                "SNOW_API_KEY": os.getenv("SNOW_API_KEY"),
                "SNOW_CLIENT_ID": os.getenv("SNOW_CLIENT_ID"),
                "SNOW_CLIENT_SECRET": os.getenv("SNOW_CLIENT_SECRET"),
                "DRAFT_KINGS_ASSEMBLED_API_KEY": os.getenv("DRAFT_KINGS_ASSEMBLED_API_KEY"),
                "ASSEMBLED_PEOPLE_TABLE": os.getenv("ASSEMBLED_PEOPLE_TABLE"),
                "DRAFT_KINGS_AGENTS_TABLE": os.getenv("DRAFT_KINGS_AGENTS_TABLE"),
                "VIVID_SEATS_AGENTS_TABLE": os.getenv("VIVID_SEATS_AGENTS_TABLE"),
            }
        else:
            # Use AWS Secrets Manager in production
            try:
                secrets_client = boto3.client("secretsmanager")
                response = secrets_client.get_secret_value(SecretId="AssembledSecrets")
                cls._secrets = json.loads(response["SecretString"])
            except Exception as e:
                error_msg = f"Error fetching secrets: {str(e)}"
                logger.error(error_msg)
                # send_slack_error(error_msg)
                raise

        return cls._secrets

    @classmethod
    def get_secret(cls, key):
        secrets = cls.get_secrets()
        return secrets.get(key)
