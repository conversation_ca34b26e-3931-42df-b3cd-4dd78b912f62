from urllib.parse import quote
import requests
import json
from secrets_util import SecretsUtil
import time
from slack_util import send_slack_error
from logger_util import get_logger

logger = get_logger(__name__)

ASSEMBLED_API_KEY = SecretsUtil.get_secret("ASSEMBLED_API_KEY")
ASSEMBLED_API_BASE_URL = SecretsUtil.get_secret("ASSEMBLED_API_BASE_URL")

def get_state(source_state):
    # "name": "Offline", "name": "Away", "name": "Available",
    # "name": "Vacation", "name": "Email", "name": "Meeting", "name": "Meal", "name": "Phone", "name": "Break", "name": "Chat",
    # return source_state.lower()
    return source_state


def get_people_by_email(email):
    email = quote(email)
    people_url = f"{ASSEMBLED_API_BASE_URL}/v0/people?search={email}"
    response = requests.get(people_url, auth=(ASSEMBLED_API_KEY, ""))
    if response.status_code != 200:
        error_msg = f"Failed to fetch people data: {response.text}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return {"error": "Failed to fetch people data"}

    people_data = response.json()
    people = list(people_data.get("people", {}).values())
    return people


def create_person(source_data):
    association_url = f"{ASSEMBLED_API_BASE_URL}/v0/people"

    people_post_response = requests.post(association_url, auth=(ASSEMBLED_API_KEY, ""), json=source_data)
    if people_post_response.status_code != 201:
        error_msg = f"Failed to add user: {people_post_response.text}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return {"error": "Failed to add user"}

    people_data = people_post_response.json()
    people = list(people_data.get("people", []))
    if people is not None and len(people) > 0:
        return people[0]

    return None
    # return people_post_response.json()


def update_person(person_id, people_data):
    association_url = f"{ASSEMBLED_API_BASE_URL}/v0/people/{person_id}"

    logger.info(f"Updating Assembled person {person_id}: {people_data}")
    people_patch_response = requests.patch(association_url, auth=(ASSEMBLED_API_KEY, ""), json=people_data)
    if people_patch_response.status_code != 200:
        error_msg = f"Failed to add imported_id: {people_patch_response.text}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return {"error": "Failed to add imported_id"}

    return people_patch_response.json()


def create_agent_association(association_data):
    association_url = f"{ASSEMBLED_API_BASE_URL}/v0/agents/associations"

    logger.info(f"Creating agent association into Assembled: {association_data}")
    association_response = requests.post(association_url, auth=(ASSEMBLED_API_KEY, ""), json=association_data)
    if association_response.status_code != 200:
        error_msg = f"Failed to create association: {association_response.text}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return False

    return True


def post_agent_state(source_data):
    state_url = f"{ASSEMBLED_API_BASE_URL}/v0/agents/state"
    # source_id = source_data.get("agent_id")
    # source_state = get_state(source_data.get("agent_state"))
    # time = source_data.get("agent_state_time")
    # state = {
    #     "agent_platform_id": source_id,
    #     "time": time,
    #     "state": source_state
    # }

    logger.info(f"Importing agent state data into Assembled: {source_data}")
    state_response = requests.post(state_url, auth=(ASSEMBLED_API_KEY, ""), json=source_data)
    if state_response.status_code != 200:
        error_msg = f"Failed to update state: {state_response.text}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return {"error": "Failed to update agent state"}

    logger.info(f"Successfully imported agent state data into Assembled")
    return state_response.json()


def post_handle_times(handle_times):
    handle_times, missing = split_valid_invalid(handle_times)
    for item in missing:
        logger.info(f"Error: Missing required fields for handle time with sys_id: {item.get('sys_id')}, ticket_platform_id: {item.get('ticket_platform_id')}, agent_platform_id: {item.get('agent_platform_id')}.")

    logger.info(f"Import handle times: importing {len(handle_times)}/{len(handle_times) + len(missing)} handle times into Assembled. {len(missing)} are missing required fields.")

    BATCH_SIZE = 1000
    MAX_RETRIES = 3
    results = {
        'successful_batches': [],
        'failed_batches': []
    }
    
    for i in range(0, len(handle_times), BATCH_SIZE):
        batch = handle_times[i:i + BATCH_SIZE]
        batch_info = {
            'start_index': i,
            'handle_times': batch,
            'attempts': 0
        }
        
        success = False
        while not success and batch_info['attempts'] < MAX_RETRIES:
            try:
                batch_info['attempts'] += 1
                handle_times_data = {"handle_times": batch}
                handle_times_url = f"{ASSEMBLED_API_BASE_URL}/v0/handle_time"
                handle_times_response = requests.post(
                    handle_times_url, 
                    auth=(ASSEMBLED_API_KEY, ""), 
                    json=handle_times_data
                )
                
                logger.info(f"Imported {len(batch)} handle times into Assembled: {handle_times_response.status_code}")
                
                if handle_times_response.status_code == 200:
                    results['successful_batches'].append({
                        'start_index': i,
                        'count': len(batch),
                        'response': handle_times_response
                    })
                    success = True
                else:
                    if batch_info['attempts'] >= MAX_RETRIES:
                        batch_info['last_error'] = handle_times_response.text
                        results['failed_batches'].append(batch_info)
                        error_msg = f"Handle times batch starting at index {i} failed after {MAX_RETRIES} attempts: {handle_times_response.text}"
                        logger.error(error_msg)
                        # send_slack_error(error_msg)
                    else:
                        time.sleep(2 ** batch_info['attempts'])
                        
            except Exception as e:
                if batch_info['attempts'] >= MAX_RETRIES:
                    batch_info['last_error'] = str(e)
                    results['failed_batches'].append(batch_info)
                    error_msg = f"Handle times batch starting at index {i} failed after {MAX_RETRIES} attempts due to exception: {str(e)}"
                    logger.error(error_msg)
                    # send_slack_error(error_msg)
                else:
                    time.sleep(2 ** batch_info['attempts'])
    
    if results['failed_batches']:
        logger.info(f"Failed batches: {results['failed_batches']}")
        error_msg = f"Completed handle times batch with {len(results['successful_batches'])} successful batches and {len(results['failed_batches'])} failed batches"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        
    return results


def post_conversations(conversations):
    BATCH_SIZE = 1000
    MAX_RETRIES = 3
    results = {
        'successful_batches': [],
        'failed_batches': []
    }
    
    for i in range(0, len(conversations), BATCH_SIZE):
        batch = conversations[i:i + BATCH_SIZE]
        batch_info = {
            'start_index': i,
            'conversations': batch,
            'attempts': 0
        }
        
        success = False
        while not success and batch_info['attempts'] < MAX_RETRIES:
            try:
                batch_info['attempts'] += 1
                conversations_data = {"conversations": batch}
                conversations_url = f"{ASSEMBLED_API_BASE_URL}/v0/conversations/bulk"
                logger.info(f"Importing conversation data into Assembled: {conversations_data}")
                conversations_bulk_response = requests.post(
                    conversations_url, 
                    auth=(ASSEMBLED_API_KEY, ""), 
                    json=conversations_data
                )
                
                if conversations_bulk_response.status_code == 200:
                    results['successful_batches'].append({
                        'start_index': i,
                        'count': len(batch),
                        'response': conversations_bulk_response
                    })
                    success = True
                else:
                    if batch_info['attempts'] >= MAX_RETRIES:
                        batch_info['last_error'] = conversations_bulk_response.text
                        results['failed_batches'].append(batch_info)
                        error_msg = f"Batch starting at index {i} failed after {MAX_RETRIES} attempts: {conversations_bulk_response.text}"
                        logger.error(error_msg)
                        # send_slack_error(error_msg)
                    else:
                        time.sleep(2 ** batch_info['attempts'])
                        
            except Exception as e:
                if batch_info['attempts'] >= MAX_RETRIES:
                    batch_info['last_error'] = str(e)
                    results['failed_batches'].append(batch_info)
                    error_msg = f"Batch starting at index {i} failed after {MAX_RETRIES} attempts due to exception: {str(e)}"
                    logger.error(error_msg)
                    # send_slack_error(error_msg)
                else:
                    time.sleep(2 ** batch_info['attempts'])
    
    if results['failed_batches']:
        error_msg = f"Completed with {len(results['successful_batches'])} successful batches and {len(results['failed_batches'])} failed batches"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        
    return results

def split_valid_invalid(handle_times):
    required_fields = {"agent_platform_id", "ticket_platform_id", "start_time", "end_time", "channel"}
    
    valid = []
    invalid = []
    
    for item in handle_times:
        # Check that all required fields exist and are not None
        if all(field in item and item[field] is not None for field in required_fields):
            valid.append({ k: item[k] for k in required_fields })
        else:
            invalid.append(item)
    
    return valid, invalid
