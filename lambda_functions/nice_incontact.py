from datetime import datetime
from secrets_util import SecretsUtil
import requests
import jwt
from jwt.exceptions import InvalidTokenError
from slack_util import send_slack_error
from logger_util import get_logger

logger = get_logger(__name__)

access_token = None
area = None
domain = None

def get_tokens():
    payload = {
        "grant_type": "password",
        "client_id": SecretsUtil.get_secret("VIVID_SEATS_CLIENT_ID"),
        "client_secret": SecretsUtil.get_secret("VIVID_SEATS_CLIENT_SECRET"),
        "username": SecretsUtil.get_secret("VIVID_SEATS_ACCESS_KEY_ID"),
        "password": SecretsUtil.get_secret("VIVID_SEATS_SECRET_KEY")
    }

    headers = {"Content-Type": "application/x-www-form-urlencoded"}

    response = requests.post(SecretsUtil.get_secret("VIVID_SEATS_BASE_AUTH_URL"), data=payload, headers=headers)
    response.raise_for_status()  # Raise exception for HTTP errors
    return response.json()


# Decode JWT to Extract tenantId
def decode_jwt(id_token):
    try:
        decoded_token = jwt.decode(id_token, options={"verify_signature": False})
        return decoded_token.get("tenantId")
    except InvalidTokenError as e:
        error_msg = f"Invalid JWT token: {e}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return None


# Get area and domain values using tenantId
def get_configuration(tenant_id):
    config_url = f"https://api-na1.niceincontact.com/.well-known/cxone-configuration?tenantId={tenant_id}"

    response = requests.get(config_url)
    response.raise_for_status()
    return response.json()


def get_token():
    # Get tokens
    tokens = get_tokens()
    global access_token
    access_token = tokens.get("access_token")
    id_token = tokens.get("id_token")

    if not id_token:
        error_msg = "id_token not found in the response."
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return None

    # Decode JWT
    tenant_id = decode_jwt(id_token)
    if not tenant_id:
        error_msg = "Unable to extract tenantId from id_token."
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return None

    # Get configuration
    config = get_configuration(tenant_id)
    global area
    area = config.get("area")

    global domain
    domain = config.get("domain")

    if not area or not domain:
        error_msg = "area or domain not found in configuration response."
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return None

    response = {
        "tenant_id": tenant_id,
        "area": area,
        "domain": domain,
        "access_token": access_token,
        "id_token": id_token,
    }

    return response


def get_nice_incontact_data(endpoint, last_run, order_by = ""):
    api_url = f"https://api-{area}.{domain}/inContactAPI/services/v{SecretsUtil.get_secret('VIVID_SEATS_NICE_INCONTACT_API_VERSION')}/{endpoint}"
    if last_run:
        api_url += f"?updatedSince={last_run}"
        logger.info(f"Fetching {endpoint} since: {last_run}")
    
    if order_by:
        api_url += f"&orderBy={order_by}"

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    response = requests.get(api_url, headers=headers)
    response.raise_for_status()
    return response.json()


def get_nice_incontact_data_next(next):
    api_url = next

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    response = requests.get(api_url, headers=headers)
    response.raise_for_status()
    return response.json()


def get_agent(agent_id):
    data = get_nice_incontact_data(f"agents/{agent_id}", None)
    agents = data.get("agents", [])
    if len(agents) > 0:
        return agents[0]

    return None


def get_agent_skills(agent_id):
    data = get_nice_incontact_data(f"agents/{agent_id}/skills", None)
    return data.get("agentSkillAssignments", [])


def get_agents(last_run):
    data = get_nice_incontact_data("agents", last_run)
    return data.get("agents", [])


def get_agent_states(last_run):
    data = get_nice_incontact_data("agents/states", last_run)
    return data.get("agentStates", [])


def get_contacts(last_run):
    logger.info(f"Fetching contacts since: {last_run}")
    data = get_nice_incontact_data("contacts", last_run)
    return data.get("contacts", [])


def get_contacts_with_end_time(start_time, end_time):
    logger.info(f"Fetching contacts between {start_time} and {end_time}")
    
    data = get_nice_incontact_data("contacts", start_time, "lastUpdateTime")
    stop_time = datetime.fromisoformat(end_time.replace("Z", "+00:00"))

    contacts = []
    
    while data:  
        for contact in data.get("contacts", []):
            last_update_time = datetime.fromisoformat(contact["lastUpdateTime"].replace("Z", "+00:00"))
            
            if last_update_time > stop_time:
                return contacts  # Early exit if we exceed stop_time
            
            contacts.append(contact)

        next_link = data.get("_links", {}).get("next")
        if not next_link:
            break  # Exit if no more pages

        data = get_nice_incontact_data_next(next_link)  # Fetch next page

    return contacts