import assembled_people
import draft_kings_people
import utilities
from logger_util import get_logger

logger = get_logger(__name__)


def get_status(source_state):
    # Assembled status options: "open", "pending", "solved" or "abandoned".
    state_mapping = {
        "1": "open",      # "New",
        "3": "solved",    # "Closed",
        "6": "solved",    # "Resolved",
        "7": "abandoned", # "Cancelled",
        "10": "open",     # "Open",
        "18": "pending",  # "Awaiting Info"
        "closed_complete": "solved",
        "work_in_progress": "open",
        "closed_abandoned": "abandoned"
    }

    return state_mapping.get(source_state, "open")


def get_channel(source_channel):
    # Assembled channel options: "phone", "email", "chat", "social" or "back_office"
    channel_mapping = {
        "email": "email",
        "social": "social",
        "phone": "phone",
        "chat": "chat",
        "web": "email",
        "messaging": "social",
        "in_person": "email",
        "proactive": "email",
        "community": "email",
        "virtual_agent": "email",
        "e_bonding": "email"
    }

    return channel_mapping.get(source_channel, "unknown")


def get_conversations(records, groups, table):
    conversations = []
    error_count = 0

    for record in records:
        created_at = utilities.get_epoch_time(record.get("sys_created_on"))
        sys_id = record.get("sys_id")

        contact_type = record.get("contact_type", record.get("type", ""))
        channel = get_channel(contact_type)
        if channel == "unknown":
            if table == "interaction":
                channel = "chat"
            else:
                channel = "email"

        state = record.get("state")
        status = get_status(state)

        resolved_at = record.get("resolved_at", record.get("closed_at"))
        if resolved_at:
            solved_at = utilities.get_epoch_time(resolved_at)
        else:
            solved_at = None

        tag_name = None
        assigned_to = record.get("assigned_to", {})
        assignee_import_id = None
        source_assignee = None
        if assigned_to:
            source_assignee = record.get("assigned_to", {}).get("value")
            servicenow_agent = draft_kings_people.get_person(source_assignee)
            if servicenow_agent.get("error") is not None:
                error_msg = f"Conversation assigned_to error: {servicenow_agent['error']}"
                logger.error(error_msg)
                # send_slack_error(error_msg)
                error_count += 1
            else:
                servicenow_email = servicenow_agent.get("source_agent_email")
                if not servicenow_email:
                    tag_name = servicenow_agent.get("source_agent_name")
                else:
                    assembled_agent = assembled_people.check_and_update_agent(servicenow_agent)
                    if assembled_agent:
                        assignee_import_id = assembled_agent.get("imported_id", None)
                        # assembled_imported_id: str = assembled_agent.get("imported_id")                        
                        # if assembled_imported_id and assembled_imported_id != assignee_import_id:
                        #     assignee_import_id = assembled_imported_id

        first_responded_at = None
        first_response_time = record.get("first_response_time")
        if first_response_time:
            first_responded_at = utilities.get_epoch_time(first_response_time)

        tags = []

        assignment_group = record.get("assignment_group")
        if isinstance(assignment_group, dict):
            assignment_group = assignment_group.get("value")
        else:
            assignment_group = None  # or any default value you prefer

        if assignment_group:
            tags = [groups.get(assignment_group)]

        tags.append(table)

        if tag_name:
            full_tag_name = f"Agent Name: {tag_name}"
            tags.append(full_tag_name)

        conversation = {
            "created_at": created_at,
            "import_id": sys_id,
            "channel": channel,
            "status": status,
            "solved_at": solved_at,
            "assignee_import_id": assignee_import_id,
            "first_responded_at": first_responded_at,
            "tags": tags
        }

        conversations.append(conversation)

    return {
        "conversations": conversations,
        "processed": len(records),
        "errors": error_count
    }
