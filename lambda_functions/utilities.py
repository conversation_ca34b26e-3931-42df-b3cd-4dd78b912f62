from datetime import datetime, timezone
from dateutil.parser import parse

def get_epoch_time(date_time):
    # Convert the string to a datetime object
    date_time_obj = parse(date_time)

    # Convert to UTC
    if date_time_obj.tzinfo is None:
        date_time_obj = date_time_obj.replace(tzinfo=timezone.utc)  # If no timezone info, assume UTC

    utc_date_time = date_time_obj

    # Convert the datetime object to a Unix epoch timestamp
    unix_timestamp = int(utc_date_time.timestamp())

    return unix_timestamp


def modify_email(email, username_plus):
    username, domain = email.split('@')
    return f"{username}+{username_plus}@{domain}"