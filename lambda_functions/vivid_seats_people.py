import nice_incontact
import utilities
import dynamodb_util
from slack_util import send_slack_error
from logger_util import get_logger

logger = get_logger(__name__)

b2b_incontact = {
    1135863, 1140180, 1137455, 1130370, 1130371, 1130372, 1130373, 1130374, 1130375, 1130376, 1130377, 1130378,
    1130379, 1130591, 1131577, 1134946, 1135864, 1135893, 1137118, 1140241
}

cs_incontact = {
    1119262, 1119263, 1119264, 1129230, 1140079, 1104112, 1126227, 1126890, 1127219, 1139282, 1139292, 1131931, 1132237,
    1124744, 1124688, 1124703, 1124087, 1129257, 1126472, 1126437, 1126350, 1125246, 1124692, 1128702, 1124694, 1129256, 1129258,
    1126740, 1124698, 1124542, 1128996, 1124701, 1124685, 1125409, 1124704, 1125391, 1126328, 1124699, 1124644, 1125669, 1124697,
    1125285, 1131956, 1103880, 1124693, 1124696, 1124683, 1124705, 1124695
}

ost_contact = {
    1119273, 1124231, 1125042, 1125245, 1139284, 1131932, 1125597, 1124457, 1124458, 1124459, 1124460, 1124461, 1124462,
    1124463, 1124464, 1125447, 1124465, 1124466, 1124467, 1124468, 1124469, 1124470, 1137679, 1137678, 1135429, 1135428,
    1124471, 1124472, 1124473, 1124474, 1124475, 1124476, 1125832, 1124477, 1129465, 1129464, 1125427, 1126034, 1125426,
    1124478, 1139190, 1139189, 1125436, 1133265, 1133264, 1125437, 1125462, 1124479, 1124480, 1133488, 1133487, 1132345,
    1132344, 1124481, 1124482, 1124483, 1124484, 1124485, 1124486, 1124487, 1124488, 1124489, 1124490, 1132343, 1132342,
    1124491, 1124492, 1125380, 1125379, 1124787, 1124493, 1126457, 1126351, 1126352, 1126450, 1126353, 1126354, 1126462,
    1126355, 1126443, 1126470, 1129467, 1126356, 1126446, 1133266, 1126447, 1126453, 1126357, 1133499, 1126358, 1126359,
    1126438, 1126435, 1126360, 1126361, 1126362, 1126363, 1126364, 1126365, 1126366, 1126367, 1126368, 1126369, 1126370,
    1126371, 1126440, 1126372, 1126373, 1126374, 1126459, 1126375, 1133500, 1126376, 1133497, 1126377, 1133496, 1126378,
    1126379, 1126456, 1135114, 1126380, 1126439, 1126381, 1126601, 1133498, 1127215, 1126382, 1126452, 1126383, 1131008,
    1126384, 1126385, 1126386, 1126387, 1126388, 1126389, 1126390, 1126391, 1126392, 1126393, 1126394, 1126395, 1126396,
    1126397, 1128842, 1126455, 1126398, 1126441, 1126399, 1126400, 1126454, 1126401, 1126554, 1126449, 1126402, 1133889,
    1131004, 1126444, 1126411, 1126403, 1126404, 1126405, 1126406, 1126407, 1126463, 1126464, 1126408, 1126465, 1126466,
    1126467, 1126409, 1126468, 1126410, 1126469, 1126413, 1126458, 1126436, 1126414, 1126415, 1126416, 1126420, 1129456,
    1126422, 1126423, 1126412, 1126471, 1126451, 1133885, 1129457, 1126417, 1126442, 1135881, 1126448, 1126419, 1126421,
    1130524, 1126418, 1129761, 1126461, 1126445, 1126424, 1126425, 1126427, 1126428, 1126429, 1126430, 1129373 ,1126431,
    1126460, 1126432, 1126433, 1126434, 1135880, 1135879, 1124494, 1124496, 1124498, 1124500, 1124502, 1124504, 1124506,
    1124508, 1124510, 1124512, 1124514, 1124515, 1125423, 1124516, 1124517, 1124518, 1124519, 1124520, 1124521, 1125655,
    1124522, 1124523, 1124524, 1124525, 1132347, 1132346, 1133489, 1133490, 1124526, 1124527, 1124528, 1124529, 1133484,
    1133483, 1135710, 1135709, 1139014, 1139013, 1124531, 1124530, 1133482, 1133481, 1124532, 1124534, 1124535, 1125592,
    1131068, 1135113, 1135112, 1124537, 1124536, 1124538, 1125405, 1124539, 1124540, 1126538, 1126600, 1126599, 1124541,
    1139156, 1135337, 1133494, 1133495, 1133491, 1135115, 1131005, 1126845, 1126741, 1126742, 1126838, 1126743, 1126744,
    1126850, 1126745, 1126831, 1126858, 1129466, 1126746, 1126834, 1133267, 1126835, 1126747, 1126841, 1126748, 1126749,
    1126826, 1126824, 1126750, 1126862, 1126752, 1126753, 1126754, 1126755, 1126756, 1126757, 1126758, 1126759, 1126760,
    1126761, 1126828, 1126762, 1126763, 1126764, 1126847, 1126765, 1126766, 1133492, 1126767, 1126768, 1126769, 1126844,
    1126770, 1126827, 1126771, 1126861, 1133493, 1127216, 1126772, 1126840, 1126773, 1131009, 1126774, 1126775, 1126776,
    1126777, 1126778, 1126779, 1126780, 1126781, 1126782, 1126783, 1126784, 1126785, 1126786, 1126787, 1128841, 1128843,
    1126788, 1126829, 1126789, 1126790, 1126842, 1126791, 1126860, 1126837, 1126792, 1133890, 1129372, 1126832, 1126801,
    1126793, 1126794, 1126795, 1126796, 1126797, 1126851, 1126852, 1126798, 1126853, 1126854, 1126855, 1126799, 1126856,
    1126800, 1126857, 1126803, 1126846, 1126825, 1126804, 1126805, 1126806, 1126810, 1129460, 1129461, 1126812, 1126813,
    1126802, 1126859, 1126839, 1133886, 1126836, 1126809, 1126807, 1126830, 1135882, 1126808, 1126811, 1130523, 1129760,
    1126849, 1126833, 1126814, 1126815, 1126816, 1126817, 1126818, 1126819, 1126820, 1126848, 1126821, 1126822, 1126823,
    1133486, 1133485, 1140333, 1140332, 1127214, 1127213, 1135558, 1135559, 1124544, 1124543, 1125461, 1124545, 1124546,
    1131007, 1131006, 1126865, 1126864, 1124548, 1124549, 1124550, 1124551, 1124552, 1124553, 1124554, 1124555, 1124556,
    1124557, 1124558, 1124559, 1124560, 1124561, 1124562, 1124563, 1124564, 1126867, 1126866, 1124565, 1124567, 1124569,
    1124570, 1124571, 1124572, 1124675, 1124573, 1126869, 1126868, 1128843, 1128839, 1138308, 1138307, 1125591, 1124574,
    1124575, 1125424, 1124576, 1124577, 1124578, 1124579, 1124580, 1124581, 1125463, 1125582, 1126553, 1126552, 1125446,
    1124583, 1124584, 1124585, 1124586, 1124587, 1124588, 1133888, 1133887, 1131003, 1131002, 1135427, 1135426, 1139155,
    1135336, 1124589, 1124650, 1124590, 1124651, 1125429, 1124591, 1124649, 1124592, 1124593, 1124594, 1124595, 1124596,
    1124597, 1124598, 1124599, 1124600, 1124601, 1124602, 1126012, 1124604, 1126013, 1124606, 1124607, 1124608, 1126014,
    1124610, 1126015, 1124612, 1126016, 1126014, 1126016, 1126017, 1124618, 1124619, 1124620, 1126018, 1124621, 1124654,
    1125611, 1125113, 1125114, 1124622, 1124655, 1124623, 1124657, 1124624, 1124660, 1124664, 1124625, 1129458, 1129459,
    1124626, 1124667, 1124627, 1124668, 1124669, 1124628, 1124630, 1124652, 1124629, 1124653, 1126291, 1125449, 1133884,
    1133883, 1124631, 1136786, 1136787, 1124666, 1125445, 1124632, 1124661, 1125425, 1124634, 1124663, 1124633, 1124665,
    1130521, 1130522, 1129747, 1129750, 1124662, 1125658, 1125433, 1124635, 1124670, 1124636, 1124671, 1124637, 1124672,
    1124638, 1124673, 1124677, 1124639, 1124640, 1124678, 1124679, 1124641, 1124680, 1129371, 1129370, 1124642, 1124681,
    1124643, 1124682, 1124645, 1124707, 1125657, 1124646, 1124709, 1124647, 1124710, 1124648, 1124711
}

sales_incontact = {
    1121773, 1122169, 1127146, 1127147, 1127148, 1119259, 1119260, 1119261, 1123935, 1127143, 1129134, 1139283
}

verification_incontact = {
    1119270, 1127034, 1127035
}

seller_ops_incontact = {
    1119267, 1135684, 1136781, 1119268, 1121837, 1119275, 1121839, 1129689, 1125531, 1125532, 1125533, 1125534,
    1125535, 1125536, 1125581, 1125584, 1125580, 1125537, 1125538, 1125539, 1125540, 1125541, 1125542, 1125543,
    1125544, 1125545, 1125546, 1125579, 1125547, 1125548, 1125549, 1125550, 1125551, 1125552, 1125553, 1125554,
    1125555, 1125556, 1125557, 1125558, 1125559, 1125560, 1125561, 1125562, 1125563, 1125564, 1125566, 1125565,
    1125568, 1125567, 1125578, 1125569, 1125570, 1125571, 1125572, 1125573, 1125582, 1125574, 1125575, 1125576,
    1125577, 1125450, 1124684
}

vdc_admin_incontact = {
    1139892, 1139895
}

vdc_air_ops_incontact = {}

vdc_incontact = {
    1139887, 1139889, 1139893, 1139944, 1139888, 1139890, 1139944
}

vdc_supervisor_incontact = {}

vsfs_incontact = {
    1133583, 1133586, 1133587, 1133588, 1133589, 1133590, 1133591, 1133592, 1133593, 1133594, 1133595, 1133736,
    1133900, 1133995, 1134214, 1134640, 1134698, 1134702, 1134702, 1134830, 1134939, 1135010, 1135022, 1135447,
    1135469, 1135486, 1135574, 1136231, 1136250, 1136301, 1136377, 1136464, 1136477, 1136718, 1136916, 1136985,
    1136987, 1137029, 1137209, 1137456, 1137711, 1137873, 1137882, 1137498, 1139663, 1139661, 1138304, 1139786,
    1140169, 1140041, 1140029, 1139030, 1139940, 1138946, 1138838, 1136985
}

def get_agent_queues(agent_skills):
    queues = []
    if agent_skills is not None and len(agent_skills) > 0:
        queues.append("84f37b58-1932-43de-9192-a7a5205cc827")
        return queues

    for skill in agent_skills:
        skill_id = skill.get("skillId")

        if skill_id in b2b_incontact and "b2b_incontact" not in queues:
            queues.append("b2b_incontact")

        if skill_id in cs_incontact and "cs_incontact" not in queues:
            queues.append("cs_incontact")

        if skill_id in ost_contact and "ost_contact" not in queues:
            queues.append("ost_contact")

        if skill_id in sales_incontact and "sales_incontact" not in queues:
            queues.append("sales_incontact")

        if skill_id in verification_incontact and "verification_incontact" not in queues:
            queues.append("verification_incontact")

        if skill_id in seller_ops_incontact and "seller_ops_incontact" not in queues:
            queues.append("seller_ops_incontact")

        if skill_id in vdc_admin_incontact and "vdc_admin_incontact" not in queues:
            queues.append("vdc_admin_incontact")

        if skill_id in vdc_air_ops_incontact and "vdc_air_ops_incontact" not in queues:
            queues.append("vdc_air_ops_incontact")

        if skill_id in vdc_incontact and "vdc_incontact" not in queues:
            queues.append("vdc_incontact")

        if skill_id in vdc_supervisor_incontact and "vdc_supervisor_incontact" not in queues:
            queues.append("vdc_supervisor_incontact")

        if skill_id in vsfs_incontact and "vsfs_incontact" not in queues:
            queues.append("vsfs_incontact")

    return queues


def get_agent_role(profile_name):
    #     return "2391ad45-b8f1-4ff5-a61c-5a2109e515bb"
    # # TODO: change above after testing in Simmered

    if profile_name == "Administrator" \
       or profile_name == "CapitalOne Entertainment" \
       or profile_name == "Dashboard Administration APPROVED" \
       or profile_name == "Default" \
       or profile_name == "NICE Integration" \
       or profile_name == "SkyBox" \
       or profile_name == "SkyBox Manager" \
       or profile_name == "VS Corporate" \
       or profile_name == "Vivid Reporting API":
        return None # Don't Map

    if profile_name == "Alorica Agent" \
       or profile_name == "Converted Agent" \
       or profile_name == "VS B2B Agent" \
       or profile_name == "VS Seller Operations" \
       or profile_name == "TaskUs Agent" \
       or profile_name == "VDC Agent" \
       or profile_name == "VS QCI" \
       or profile_name == "VS ERT" \
       or profile_name == "VS Concierge Services" \
       or profile_name == "VS Social Media" \
       or profile_name == "VS Fraud" \
       or profile_name == "VSFS":
        return "342d28ba-28bd-414a-be5d-fb8fe65aa8f6"  # Standard

    if profile_name == "Alorica Command Center APPROVED" \
        or profile_name == "Alorica Manager" \
        or profile_name == "TaskUs Command Center APPROVED" \
        or profile_name == "TaskUs Manager":
        return "ff23d9b5-b765-4712-9206-bff939e588f4"  # BPO Manager

    if profile_name == "Vivid Admin APPROVED" \
        or profile_name == "Vivid Super Admin APPROVED":
        return "ff23d9b5-b765-4712-9206-bff939e588f4"  # Manager

    if profile_name == "VS Command Center":
        return "1a9e71ec-7359-4c64-9ddb-3a7bfbcf0314"  # Admin

    if profile_name == "Converted Manager" \
       or profile_name == "VS B2B Manager" \
       or profile_name == "VDC Admin" \
       or profile_name == "VDC Lead" \
       or profile_name == "Vivid Manager APPROVED" \
       or profile_name == "VS QA" \
       or profile_name == "VS Supervisor" \
       or profile_name == "VS Trainers" \
       or profile_name == "VSFS Manager":
        return  "a1fc0b84-bf76-4b41-854f-b933e9e6aba0"  # Team Lead

    return None


def get_agent_channels(source_agent):
    channels = []

    chat_threshold = source_agent.get("chatThreshold")
    email_threshold = source_agent.get("emailThreshold")
    work_item_threshold = source_agent.get("workItemThreshold")
    voice_threshold = source_agent.get("voiceThreshold")

    if chat_threshold > 0:
        channels.append("chat")

    if email_threshold > 0:
        channels.append("email")

    if voice_threshold > 0:
        channels.append("phone")

    if work_item_threshold > 0:
        channels.append("back_office")

    return channels


def get_agent_site(location):
    if location is not None:
        return "595e56b8-7d65-4a54-9220-90e6b181437c"
    # TODO: remove for prod

    if location == "Mohali":
        return ""
    elif location == "Davao":
        return ""
    elif location == "Chicago":
        return ""
    elif location == "Dallas":
        return ""
    elif location == "Canda":
        return ""
    elif location == "Las Vegas":
        return ""
    elif location == "Bantangas":
        return ""
    elif location == "Columbia":
        return ""

    return None


def get_agent_team(team_id):
    if team_id is not None:
        return "2b74cc2c-1878-48a0-b1bf-42e65b75213f"
    # TODO: Remove for prod

    if team_id == 803180:
        return "BPO Alorica > BPO Alorica CS Davao"
    if team_id == 803181:
        return "BPO Alorica > BPO Alorica OST Mohali"
    if team_id == 803261:
        return "BPO Alorica > BPO Alorica Sales Davaov"
    if team_id == 803182:
        return "BPO Alorica > BPO Alorica Team Lead"
    if team_id == 802872:
        return "BPO TaskUs > BPO TaskUs CS Batangas"
    if team_id == 803349:
        return "BPO TaskUs >BPO TaskUs CS Medellin"
    if team_id == 802790:
        return "BPO TaskUs > BPO TaskUs OST Batangas"
    if team_id == 803352:
        return "BPO TaskUs > BPO TaskUs Sales Batangas"
    if team_id == 803350:
        return "BPO TaskUs > BPO TaskUS Sales Medellin"
    if team_id == 802791:
        return "BPO TaskUs > BPO TaskUs Team Lead"
    if team_id == 800143:
        return "ERT"
    if team_id == 801177:
        return "ERT> ERT Subject Leads"
    if team_id == 802013:
        return "QCI"
    if team_id == 800173:
        return "Seller Operations"
    if team_id == 803218:
        return "Seller Operations > Seller Ops Team Leads"
    if team_id == 802854:
        return "Social Media"
    if team_id == 803371:
        return "VDC > VDC Air Operations"
    if team_id == 803372:
        return "VDC> VDC Contact Center"
    if team_id == 803374:
        return "VDC> VDC Operations"
    if team_id == 803373:
        return "VDC> VDC Supervisor"
    if team_id == 800147:
        return "Verification"
    if team_id == 800367:
        return "Verification Team Leads"
    if team_id == 700395:
        return "VSFS"

    return None


def get_agent_time_zone(time_zone):
    # "(GMT-07:00) Mountain Time (US & Canada)"
    # "(GMT-06:00) Central Time (US & Canada)"
    # "(GMT+08:00) Beijing, Chongqing, Hong Kong, Urumqi"
    # "(GMT-05:00) Eastern Time (US & Canada)"
    return "America/Chicago"


def get_agent(agent_id):
    cached_agent = dynamodb_util.get_cached_vivid_seats_agent(agent_id)
    if cached_agent:
        logger.info(f"Found cached Vivid Seats agent for ID: {agent_id}")
        return cached_agent

    filter = f"/{agent_id}"
    # if "@" in agent_id:
    #     filter = f"?sysparm_query=email={agent_id}"

    # Fetch user from source
    source_agent_response = nice_incontact.get_agent(filter)

    # Check if the response is a dictionary with user details
    if not source_agent_response or not isinstance(source_agent_response, dict):
        error_msg = f"Unexpected or empty response for agent ID: {agent_id}. Response: {source_agent_response}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return {"error": f"Failed to retrieve agent {agent_id}"}

    if source_agent_response and not source_agent_response.get("error"):
        dynamodb_util.cache_vivid_seats_agent(agent_id, source_agent_response)

    return source_agent_response


def get_agent_skills(agent_id):
    filter = f"/{agent_id}"

    # Fetch user from source
    source_agent_skills_response = nice_incontact.get_agent_skills(filter)

    # Check if the response is a dictionary with user details
    if not source_agent_skills_response:
        error_msg = f"Unexpected or empty response for agent skills for agent ID: {agent_id}. Response: {source_agent_skills_response}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return {"error": f"Failed to retrieve agent skills for agent {agent_id}"}

    return source_agent_skills_response


def get_person(agent_id):
    # Fetch user from source
    source_agent_response = get_agent(agent_id)
    if source_agent_response.get("error"):
        error_msg = f"Unexpected or empty response for agent ID: {agent_id}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return {"error": source_agent_response["error"]}

    agent_user_id = source_agent_response["userId"]
    agent_email = source_agent_response.get("emailAddress")
    agent_first_name = source_agent_response.get("firstName")
    agent_last_name = source_agent_response.get("lastName")
    agent_name = f"{agent_first_name} {agent_last_name}"
    agent_time_zone = source_agent_response.get("timeZone")
    create_date = source_agent_response.get("createDate")
    agent_start_date = utilities.get_epoch_time(create_date)
    agent_end_date = None
    agent_active = True # source_agent_response.get("isActive")
    if not agent_active:
        agent_end_date = utilities.get_epoch_time(source_agent_response.get("inactiveDate"))


    agent_hire_date = source_agent_response.get("hireDate")
    if agent_hire_date:
        agent_hire_date = utilities.get_epoch_time(agent_hire_date)

    agent_termination_date = source_agent_response.get("terminationDate")
    if agent_termination_date:
        agent_termination_date = utilities.get_epoch_time(agent_termination_date)

    if not agent_email or not agent_name:
        error_msg = f"Email or name missing for agent ID. Email: {agent_email}, Name: {agent_name}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return {"error": f"Failed to retrieve agent {agent_id}"}

    # Teams
    agent_team_id = source_agent_response.get("teamId")
    agent_team = get_agent_team(agent_team_id)
    teams = [agent_team]

    # Role
    agent_profile_name = source_agent_response.get("profileName")
    role = get_agent_role(agent_profile_name)

    # Channels
    channels = get_agent_channels(source_agent_response)

    # Staffable
    staffable = True if channels else False

    # Queues
    source_agent_skills_response = get_agent_skills(agent_id)
    queues = get_agent_queues(source_agent_skills_response)

    # Site
    location = source_agent_response.get("location")
    site = get_agent_site(location)

    # Time Zone
    time_zone = get_agent_time_zone(agent_time_zone)

    # Prepare Assembled data
    source_data = {
        "source_agent_active": agent_active,
        "source_agent_id": str(agent_id),
        "source_agent_email": agent_email,
        "source_agent_first_name": agent_first_name,
        "source_agent_last_name": agent_last_name,
        "source_agent_name": agent_name,
        "source_agent_start_date": agent_start_date,
        "source_agent_end_date": agent_end_date,
        "source_agent_role": role,
        "source_agent_channels": channels,
        "source_agent_queues": queues,
        "source_agent_teams": teams,
        "source_agent_site": site,
        "source_agent_time_zone": time_zone,
        "source_agent_staffable": staffable
    }

    return source_data