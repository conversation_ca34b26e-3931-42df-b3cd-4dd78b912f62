import boto3
import json
import os
from datetime import datetime, timedelta
from decimal import Decimal
from slack_util import send_slack_error
from logger_util import get_logger

logger = get_logger(__name__)

# Initialize DynamoDB client
dynamodb = boto3.resource('dynamodb')

assembled_people_table = dynamodb.Table(os.environ['ASSEMBLED_PEOPLE_TABLE'])
draft_kings_agents_table = dynamodb.Table(os.environ['DRAFT_KINGS_AGENTS_TABLE'])
vivid_seats_agents_table = dynamodb.Table(os.environ['VIVID_SEATS_AGENTS_TABLE'])

CACHE_TTL = 24 * 60 * 60

def get_cached_assembled_person(email):
    try:
        response = assembled_people_table.get_item(
            Key={'email': email}
        )
        
        if 'Item' not in response:
            return None
            
        item = response['Item']
        ttl = int(item['ttl']) if isinstance(item['ttl'], Decimal) else item['ttl']
        if datetime.fromtimestamp(ttl) < datetime.now():
            return None
            
        return item['data']
    except Exception as e:
        error_msg = f"Error retrieving cached Assembled person: {str(e)}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return None

def cache_assembled_person(email, data):
    try:
        ttl = int((datetime.now() + timedelta(seconds=CACHE_TTL)).timestamp())
        assembled_people_table.put_item(
            Item={
                'email': email,
                'data': data,
                'ttl': ttl,
                'cached_at': datetime.now().isoformat()
            }
        )
        return True
    except Exception as e:
        error_msg = f"Error caching Assembled person: {str(e)}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return False

def get_cached_draft_kings_agent(agent_id):
    try:
        response = draft_kings_agents_table.get_item(
            Key={'agent_id': agent_id}
        )
        
        if 'Item' not in response:
            return None
            
        item = response['Item']
        ttl = int(item['ttl']) if isinstance(item['ttl'], Decimal) else item['ttl']
        if datetime.fromtimestamp(ttl) < datetime.now():
            return None
            
        return item['data']
    except Exception as e:
        error_msg = f"Error retrieving cached Draft Kings agent: {str(e)}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return None

def cache_draft_kings_agent(agent_id, data):
    try:
        ttl = int((datetime.now() + timedelta(seconds=CACHE_TTL)).timestamp())
        draft_kings_agents_table.put_item(
            Item={
                'agent_id': agent_id,
                'data': data,
                'ttl': ttl,
                'cached_at': datetime.now().isoformat()
            }
        )
        return True
    except Exception as e:
        error_msg = f"Error caching Draft Kings agent: {str(e)}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return False

def get_cached_vivid_seats_agent(agent_id):
    try:
        response = vivid_seats_agents_table.get_item(
            Key={'agent_id': agent_id}
        )
        
        if 'Item' not in response:
            return None
            
        item = response['Item']
        ttl = int(item['ttl']) if isinstance(item['ttl'], Decimal) else item['ttl']
        if datetime.fromtimestamp(ttl) < datetime.now():
            return None
            
        return item['data']
    except Exception as e:
        error_msg = f"Error retrieving cached Vivid Seats agent: {str(e)}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return None

def cache_vivid_seats_agent(agent_id, data):
    try:
        ttl = int((datetime.now() + timedelta(seconds=CACHE_TTL)).timestamp())
        vivid_seats_agents_table.put_item(
            Item={
                'agent_id': agent_id,
                'data': data,
                'ttl': ttl,
                'cached_at': datetime.now().isoformat()
            }
        )
        return True
    except Exception as e:
        error_msg = f"Error caching Vivid Seats agent: {str(e)}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return False 