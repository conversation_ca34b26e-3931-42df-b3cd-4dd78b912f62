import requests
import secrets_util
from slack_util import send_slack_error
from logger_util import get_logger

logger = get_logger(__name__)

# API endpoints
SNOW_AGENT_PRESENCE_TABLE = "awa_agent_presence"
SNOW_PRESENCE_STATE_TABLE = "awa_presence_state"
SNOW_USER_TABLE = "sys_user"
SNOW_USER_GROUPS_TABLE = "sys_user_group"
SNOW_CASE_TABLE = "x_drk_cx_csm_cx_case"
SNOW_INTERACTION_TABLE = "interaction"
SNOW_TASK_TABLE = "sn_customerservice_task"
SNOW_TIMES_TABLE = "x_drk_dk_timetrack_time_worked"

SNOW_API_KEY = secrets_util.SecretsUtil.get_secret("SNOW_API_KEY")
SNOW_SUBDOMAIN = secrets_util.SecretsUtil.get_secret("SNOW_SUBDOMAIN")
SNOW_CLIENT_ID = secrets_util.SecretsUtil.get_secret("SNOW_CLIENT_ID")
SNOW_CLIENT_SECRET = secrets_util.SecretsUtil.get_secret("SNOW_CLIENT_SECRET")

access_token: str = ""

# Additional request parameters
SNOW_HEADERS = {
    "x-sn-apikey": SNOW_API_KEY
}


def get_token():
    global access_token
    url = f"https://{SNOW_SUBDOMAIN}.service-now.com/oauth_token.do"
    scope = "table_api_read"
    grant_type = "client_credentials"

    # Request payload
    payload = {
        "grant_type": "client_credentials",
        "client_id": SNOW_CLIENT_ID,
        "client_secret": SNOW_CLIENT_SECRET,
        "scope": "table_api_read",
    }

    # Make the request
    response = requests.post(url, data=payload)

    # Check response
    if response.status_code == 200:
        token_data = response.json()
        access_token = token_data.get("access_token")
        return access_token
    else:
        error_msg = f"Error: {response.status_code} - {response.text}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return None


def fetch_snow_records(table, endpoint_filter):
    # Fetch records from ServiceNow table with optional filter.
    try:
        global access_token
        if not access_token:
            get_token()
        
        url = f"https://{SNOW_SUBDOMAIN}.service-now.com/api/now/table/{table}{endpoint_filter}"
        logger.info(f"Requesting data from: {url}")
        response = requests.get(url, headers = {"Authorization": f"Bearer {access_token}"}) #headers=SNOW_HEADERS)
        # response = requests.get(url, headers=SNOW_HEADERS)
        response.raise_for_status()
        data = response.json()
        return data.get("result", [])
    except requests.exceptions.RequestException as e:
        error_msg = f"An error occurred: {e}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return []


def get_agent_presence_states():
    # Fetch agent presence records
    agent_presence_records = fetch_snow_records(SNOW_PRESENCE_STATE_TABLE, "")
    return agent_presence_records


def get_groups():
    group_records = fetch_snow_records(SNOW_USER_GROUPS_TABLE, "")
    return group_records


def get_agent_presence_records(endpoint_filter):
    agent_presence_records = fetch_snow_records(SNOW_AGENT_PRESENCE_TABLE, endpoint_filter)
    return agent_presence_records


def get_user(endpoint_filter):
    snow_agent_response = fetch_snow_records(SNOW_USER_TABLE, endpoint_filter)
    return snow_agent_response


def get_cases(snow_case_table, endpoint_filter):
    if snow_case_table == "":
        snow_case_table = SNOW_CASE_TABLE

    snow_case_response = fetch_snow_records(snow_case_table, endpoint_filter)
    return snow_case_response

def get_time_worked(from_date):
    endpoint_filter = f"?sysparm_query=sys_updated_on>{from_date}&sysparm_order_by=sys_updated_on"
    all_records = []
    records = get_time_worked_records(endpoint_filter)
    while (len(records) == 10000):
        # find the oldest record
        records = sorted(records, key=lambda k: k['sys_updated_on'])
        new_filter = f"?sysparm_query=sys_updated_on>{records[-1]['sys_updated_on']}&sysparm_order_by=sys_updated_on"
        
        # empty the accumulator
        all_records.extend(records)
        records = []
        
        # get the next page
        records = get_time_worked_records(new_filter)

    all_records.extend(records)
    return all_records

def get_time_worked_records(endpoint_filter):
    global access_token
    if not access_token:
        get_token()
    
    url = f"https://{SNOW_SUBDOMAIN}.service-now.com/api/now/table/{SNOW_TIMES_TABLE}{endpoint_filter}"
    logger.info(f"Requesting data from: {url}")
    response = requests.get(url, headers = {"Authorization": f"Bearer {access_token}"})
    response.raise_for_status()
    data = response.json()
    return data.get("result", [])
        