import assembled_people
import avalara_people as avalara_people
import utilities
from slack_util import send_slack_error
from logger_util import get_logger

logger = get_logger(__name__)


skill_ids = {
    4294416, 4294414, 4309301, 10664220, 20938515, 16440029, 8419195, 6362197, 10791038, 10791037, 20731311,
    4294427, 4294431, ********, 6362198, 4294478, 4294479, ********, ********, 8419287, 6362199, ********, ********
}

def get_queue(skill_id):
    if skill_id is None:
        return None

    if skill_id in skill_ids:
        return "6f18db68-6888-4064-a888-6cb9f2dd3008"   # Customer Support

    return None


def get_status(source_state, abandoned):
    # Assembled status options: "open", "pending", "solved" or "abandoned".
    # Nice Incontact states:
        # Undefined [-1]
        # Abandoned [1]
        # Routing [2]
        # Released [3]
        # Active [4]
        # Transfer [5]
        # Inbound [6]
        # Outbound [7]
        # Hold [8]
        # Prequeue [9]
        # Inqueue [10]
        # PostQueue [11]
        # Interrupted [12]
        # Busy [13]
        # Conference [14]
        # CallBack [15]
        # Spawned [16]
        # PlaceCall [17]
        # EndContact [18]
        # EmailDiscard [19]
        # EmailReply [20]
        # EmailForward [21]
        # EmailTransfer [22]
        # CallbackPending [23]
        # Refused [24]
        # Preview [25]
        # OutboundPending [26]
        # PauseDetection [27]
        # OutboundAbandon [28]
        # Answered [29]
        # PrequeueAbandoned [30]
        # OutboundCalledPartyHangUp [31]
        # Parked [32]
        # InQueuePreview [33]
        # OutboundAnswered [34]

    if abandoned:
        return "abandoned"

    match source_state:
        case 2 | 4 | 5 | 6 | 7 | 14 | 15 | 17 | 25 | 26 | 29 | 34:
            return "open"
        case 3 | 11 | 18 | 31:
            return "solved"
        case 8 | 10 | 13 | 23 | 32 | 33:
            return "pending"
        case 1 | 24 | 28 | 30:
            return "abandoned"
        case _:
            return None


def get_channel(media_type_id):
    # Assembled channel options: "phone", "email", "chat", "social" or "back_office"
    if media_type_id == 1:      # Email
        return "email"
    elif media_type_id == 2:    # FAX
        return None
    elif media_type_id == 3:    # Chat
        return "chat"
    elif media_type_id == 4:    # PHone Call
        return "phone"
    elif media_type_id == 5:    # Voice Mail
        return "phone"
    elif media_type_id == 6:    # Work Item
        return "sms"
    elif media_type_id == 7:    # SMS
        return "sms"
    elif media_type_id == 8:    # Social
        return "social"
    elif media_type_id == 9:    # Digital
        return None

    return None


def get_conversations(records):
    conversations = []

    for record in records:
        # import_id
        contact_id = str(record.get("contactId"))

        # Queue
        skill_id = record.get("skillId")
        queue = get_queue(skill_id)
        if queue is None:
            log_msg = f"Contact {contact_id}: Skill Id {skill_id} has no queue - do not import"
            logger.info(log_msg)
            continue

        # Status
        abandoned = record.get("abandoned")
        state_id = record.get("stateId")
        status = get_status(state_id, abandoned)
        if status is None:
            log_msg = f"Status {status} not found for source value of '{state_id} - do not import'."
            logger.info(log_msg)
            continue

        # Channel
        media_type_id = record.get("mediaTypeId")
        channel = get_channel(media_type_id)
        if channel is None:
            log_msg = f"Channel not found for source value of '{media_type_id}'."
            logger.info(log_msg)
            continue

        # Assignee Import Id
        agent_id = record.get("agentId")
        if agent_id:
            assignee_import_id = agent_id
            source_agent = avalara_people.get_person(assignee_import_id)
            if source_agent.get("error") is not None:
                error_msg = f"Conversation assigned_to error: {source_agent['error']}"
                logger.error(error_msg)
                # send_slack_error(error_msg)
                assignee_import_id = None

            # If the agent team is not Product Support, do not import the conversation
            source_agent_team = source_agent.get("source_agent_teams")
            if not source_agent_team or source_agent_team != 2449843:   # Empty or not Product Support
                log_msg = f"Conversation agent {agent_id} team Id {source_agent_team} - do not import conversation"
                logger.info(log_msg)
                continue

            assembled_agent = assembled_people.check_and_update_agent(source_agent)
            if assembled_agent is not None:
                assembled_imported_id: str = assembled_agent.get("imported_id")
                if assembled_imported_id != assignee_import_id:
                    assignee_import_id = assembled_imported_id
            else:
                assignee_import_id = None
        else:
            assignee_import_id = None

        # Created At
        created_at = utilities.get_epoch_time(record.get("contactStartDate"))

        # Solved At
        is_active = record.get("isActive")
        solved_at = None
        if not is_active:
            last_update_time = record.get("lastUpdateTime")
            if last_update_time is not None:
                solved_at = utilities.get_epoch_time(last_update_time)

        # First Responded At
        first_responded_at = None
        first_response_time = record.get("agentStartDate")
        if first_response_time:
            first_responded_at = utilities.get_epoch_time(first_response_time)

        # Tags
        tags = []
        skill_name = record.get("skillName")
        if skill_name:
            tags.append(skill_name)

        conversation = {
            "created_at": created_at,
            "import_id": contact_id,
            "channel": channel,
            "status": status,
            "queue": queue,
            "first_responded_at": first_responded_at,
            "solved_at": solved_at,
            "assignee_import_id": assignee_import_id,
            "tags": tags
        }

        conversations.append(conversation)

    return conversations