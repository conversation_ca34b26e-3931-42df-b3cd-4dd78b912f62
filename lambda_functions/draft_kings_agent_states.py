import utilities
import assembled_agent_states
import draft_kings_people
from slack_util import send_slack_error
from logger_util import get_logger

logger = get_logger(__name__)

def update_presence_states(presence_state_map, agent_presence_records):
    """
    Fetch and match agent presence records, check platform IDs, and update states in Assembled.
    """
    error_count = 0

    # Loop through agent presence records
    for agent_record in agent_presence_records:
        agent_id = agent_record.get("agent", {}).get("value")
        state_id = agent_record.get("current_presence_state", {}).get("value")
        matching_state = presence_state_map.get(state_id)

        if not matching_state:
            error_msg = f"No matching state for sys_id: {agent_id}"
            logger.error(error_msg)
            # send_slack_error(error_msg)
            error_count += 1
            continue

        logger.info(f"Processing sys_id: {agent_id}, State: {matching_state}")

        # Fetch user from ServiceNow
        snow_data = draft_kings_people.get_person(agent_id)
        if snow_data.get("error") is not None:
            error_msg = f"Error processing agent {agent_id}: {snow_data['error']}"
            logger.error(error_msg)
            # send_slack_error(error_msg)
            error_count += 1
            continue

        # Not really an "error", but we cannot process the agent state - should never happen, but possible config error in ServiceNow
        source_agent_email = snow_data["source_agent_email"]
        if not source_agent_email:
            error_msg = f"Agent email address not present for agent_id {agent_id} - cannot import agent state"
            logger.error(error_msg)
            # send_slack_error(error_msg)
            error_count += 1
            continue

        updated_at = agent_record.get("sys_updated_on")
        epoch_time = utilities.get_epoch_time(updated_at)

        snow_data["source_state"] = matching_state
        snow_data["source_state_time"] = epoch_time

        # Check and update agent in Assembled
        result = assembled_agent_states.update_agent_state(snow_data)
        if result is None:
            error_count += 1
            error_msg = f"Failed to update agent state for {agent_id}"
            logger.error(error_msg)
            # send_slack_error(error_msg)

    return {"processed": len(agent_presence_records), "errors": error_count}