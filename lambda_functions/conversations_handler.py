import json
import datetime
import draft_kings_conversations
import servicenow
import assembled_conversations
from logger_util import get_logger

logger = get_logger(__name__)

def handler(event, context):
    try:
        # Calculate startDateTime as 17 minutes before current execution time to give buffer.
        current_time = datetime.datetime.utcnow()
        startDateTime = (current_time - datetime.timedelta(minutes=17)).strftime(
            "%Y-%m-%d %H:%M:%S"
        )

        endpoint_filter = f"?sysparm_query=sys_updated_on>{startDateTime}&sysparm_order_by=sys_updated_on"

        # Fetch assignment groups
        logger.info("Fetching groups")
        groups_records = servicenow.get_groups()
        if not groups_records:
            error_msg = "Failed to fetch groups from ServiceNow"
            logger.error(error_msg)
            # send_slack_error(error_msg)
            return {
                "statusCode": 500,
                "body": json.dumps({"error": error_msg})
            }

        assignment_group_map = {
            record["sys_id"]: record["name"] for record in groups_records
        }

        total_processed = 0
        total_errors = 0
        results = {}

        # Process cases
        logger.info("Fetching Cases")
        records = servicenow.get_cases("x_drk_cx_csm_cx_case", endpoint_filter)
        if records:
            logger.info("Processing Cases")
            result = draft_kings_conversations.get_conversations(records, assignment_group_map, "case")
            total_processed += result["processed"]
            total_errors += result["errors"]
            if result["conversations"]:
                logger.info("Posting Cases")
                post_cases_response = assembled_conversations.post_conversations(result["conversations"])
                if post_cases_response is None:
                    total_errors += 1
            results["cases"] = result

        # Process interactions
        logger.info("Fetching Interactions")
        records = servicenow.get_cases("interaction", endpoint_filter)
        if records:
            logger.info("Processing Interactions")
            result = draft_kings_conversations.get_conversations(records, assignment_group_map, "interaction")
            total_processed += result["processed"]
            total_errors += result["errors"]
            if result["conversations"]:
                logger.info("Posting Interactions")
                post_interactions_response = assembled_conversations.post_conversations(result["conversations"])
                if post_interactions_response is None:
                    total_errors += 1
            results["interactions"] = result

        # Process tasks
        logger.info("Fetching Tasks")
        records = servicenow.get_cases("sn_customerservice_task", endpoint_filter)
        if records:
            logger.info("Processing Tasks")
            result = draft_kings_conversations.get_conversations(records, assignment_group_map, "task")
            total_processed += result["processed"]
            total_errors += result["errors"]
            if result["conversations"]:
                logger.info("Posting Tasks")
                post_tasks_response = assembled_conversations.post_conversations(result["conversations"])
                if post_tasks_response is None:
                    total_errors += 1
            results["tasks"] = result

        return {
            "statusCode": 200,
            "body": json.dumps({
                "message": "Conversations processed successfully",
                "total_processed": total_processed,
                "total_errors": total_errors,
                "results": results
            })
        }
    except Exception as e:
        error_msg = f"Unexpected error in conversations handler: {str(e)}"
        logger.error(error_msg, exc_info=True)
        # send_slack_error(error_msg)
        return {
            "statusCode": 500,
            "body": json.dumps({"error": error_msg})
        }
