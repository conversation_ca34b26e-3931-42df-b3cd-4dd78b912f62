import requests
import nice_incontact
import vivid_seats_conversations
import assembled_conversations
from datetime import datetime, timed<PERSON><PERSON>


def handler(event, context):
    try:
        token_response = nice_incontact.get_token()
        if token_response is None:
            return {
                "statusCode": 400,
                "body": "Failed to obtain token"
            }

        # Get contacts (conversations)
        start_time = (datetime.now() - timedelta(minutes=31)).strftime("%Y-%m-%dT%H:%M:%SZ")
        end_time = (datetime.now() - timedelta(minutes=15)).strftime("%Y-%m-%dT%H:%M:%SZ")
        contacts = nice_incontact.get_contacts_with_end_time(start_time, end_time)
        conversations = vivid_seats_conversations.get_conversations(contacts)
        post_cases_response = assembled_conversations.post_conversations(conversations)

        return {
            "statusCode": 200,
            "body": {
                "message": "Conversations processed successfully",
                "result": post_cases_response
            }
        }

    except requests.exceptions.RequestException as e:
        return {
            "statusCode": 500,
            "body": f"An error occurred with the API request: {str(e)}"
        } 