import assembled_people
import vivid_seats_people as vivid_seats_people
import utilities
from datetime import datetime, timedelta
from slack_util import send_slack_error
from logger_util import get_logger

logger = get_logger(__name__)


def get_queue(campaign_id):
    if campaign_id is not None:
        return "84f37b58-1932-43de-9192-a7a5205cc827"
    # TODO: remove for prod

    if campaign_id == 901763:
        return "CS queue> CS-Incontact child queue> phone channel"
    elif campaign_id == 901764 or campaign_id == 902245 or campaign_id == 902388:
        return "OST> Chat & Email channels"
    elif campaign_id == 901770 or campaign_id == 904252:
        return "Sales>  Sales- Incontact child queue> phone channel"
    elif campaign_id == 901768 or campaign_id == 902798:
        return "Fraud Verfication> Verification- Incontact child queue> backoffice and phone channels"
    elif campaign_id == 903156 or campaign_id == 903867 or campaign_id == 904057:
        return "B2B> B2B- Incontact child queue> chat,phone channel"
    elif campaign_id == 903074 or campaign_id == 902350 or campaign_id == 901769 or campaign_id == 901765:
        return "Seller Ops> Seller Ops- Incontact child queue> phone,chat, sms channel"
    elif campaign_id == 903586:
        return "VSFS> VSFS- Incontact child queue> Phone channel"
    elif campaign_id == 904383 or campaign_id == 904384:
        return "VDC Sales & CS> VDC - Incontact child> Phone channel"
    elif campaign_id == 904386:
        return "VDC Admin> VDC Admin- Incontact child queue> phone channel"
    elif campaign_id == 904387:
        return "VDC Air Ops> VDC Air Ops-Incontact child queue> phone channel"
    elif campaign_id == 904385:
        return "VDC Supervisor> VDC Supervisor- Incontact> phone channel"
    elif campaign_id == 903212:
        return None
    elif campaign_id == 904388:
        return None

    return None


def get_status(source_state, abandoned):
    # Assembled status options: "open", "pending", "solved" or "abandoned".
    if abandoned:
        return "abandoned"
    elif source_state == 4:   # Active
        return "open"
    elif source_state == 6:   # Inbound
        return "open"
    elif source_state == 7:   # Outbound
        return "open"
    elif source_state == 8:   # Hold
        return "open"
    elif source_state == 12:  # Interrupted
        return "open"
    elif source_state == 13:  # Conference
        return "open"
    elif source_state == 14:  # Busy
        return "open"
    elif source_state == 23:  # CallbackPending
        return "pending"
    elif source_state == 26:  # OutboundPending
        return "pending"
    elif source_state == 2:   # Routing
        return "pending"
    elif source_state == 10:  # Inqueue
        return "pending"
    elif source_state == 15:  # CallBack
        return "pending"
    elif source_state == 24:  # Refused
        return "abandoned"
    elif source_state == 33:  # InQeueuePreview
        return "pending"
    elif source_state == 25:  # Preview
        return "pending"
    elif source_state == 27:  # PauseDetection
        return "open"
    elif source_state == 28:  # OutboundAbandon
        return "abandoned"
    elif source_state == 29:  # Answered
        return "open"
    elif source_state == 31:  # OutboundCalledPartyHangUp
        return "abandoned"
    elif source_state == 34:  # OutboundAnswered
        return "open"
    elif source_state == 5:   # Transfer
        return "pending"
    elif source_state == 11:  # PostQueue
        return "solved"
    elif source_state == 1:   # Abandoned
        return "abandoned"
    elif source_state == 3:   # Released
        return "solved"
    elif source_state == 18:  # EndContact
        return "solved"
    # elif source_state == 19:  # EmailDiscard
    #     return "solved"
    elif source_state == 20:  # EmailReply
        return "solved"
    elif source_state == 21:  # EmailForward
        return "solved"
    elif source_state == 22:  # EmailTransfer
        return "solved"

    return None


def get_channel(media_type_id):
    # Assembled channel options: "phone", "email", "chat", "social" or "back_office"
    if media_type_id == 1:      # Email
        return "email"
    elif media_type_id == 2:    # FAX
        return None
    elif media_type_id == 3:    # Chat
        return "chat"
    elif media_type_id == 4:    # PHone Call
        return "phone"
    elif media_type_id == 5:    # Voice Mail
        return "phone"
    elif media_type_id == 6:    # Work Item
        return "sms"
    elif media_type_id == 7:    # SMS
        return "sms"
    elif media_type_id == 8:    # Social
        return "social"
    elif media_type_id == 9:    # Digital
        return None

    return None


def get_conversations(records):
    conversations = []

    for record in records:
        # Only import mapped states
        abandoned = record.get("abandoned")
        state_id = record.get("stateId")
        status = get_status(state_id, abandoned)
        if status is None:
            logger.warning(f"Status {status} not found for source value of '{state_id} - do not import'.")
            continue

        # Adjust the contactStartDate to be after the prequeue
        contact_start_date = record.get("contactStartDate")
        pre_queue_seconds = record.get("preQueueSeconds")
        last_update_time = record.get("lastUpdateTime")

        contact_start_dt = datetime.fromisoformat(contact_start_date.replace("Z", "+00:00"))
        created_at_dt = contact_start_dt + timedelta(seconds=pre_queue_seconds)
        last_update_dt = datetime.fromisoformat(last_update_time.replace("Z", "+00:00"))
        seconds_difference = (last_update_dt - created_at_dt).total_seconds()

        # If it went from start to EndContact in less than three seconds, do not import
        if state_id == 18 and seconds_difference < 3:  # EndContact
            logger.warning(f"EndContact in two seconds - do not import'.")
            continue

        # If the channel is not found, do not import
        media_type_id = record.get("mediaTypeId")
        channel = get_channel(media_type_id)
        if channel is None:
            logger.warning(f"Channel not found for source value of '{media_type_id}'.")
            continue

        # If the agent does not have a role assigned, do not import
        agent_id = record.get("agentId")
        if agent_id:
            assignee_import_id = agent_id
            source_agent = vivid_seats_people.get_person(assignee_import_id)
            if source_agent.get("error") is not None:
                error_msg = f"Conversation assigned_to error: {source_agent['error']}"
                logger.error(error_msg)
                # send_slack_error(error_msg)
                continue

            # If the agent role is not set, do not import the conversation
            if source_agent.get("source_agent_role") is None:
                logger.warning(f"Conversation agent {agent_id} does not have a role assigned - do not import conversation")
                continue

            assembled_agent = assembled_people.check_and_update_agent(source_agent)
            if assembled_agent is not None:
                assembled_imported_id: str = assembled_agent.get("imported_id")
                if assembled_imported_id != assignee_import_id:
                    assignee_import_id = assembled_imported_id
            else:
                assignee_import_id = None
        else:
            assignee_import_id = None


        created_at = utilities.get_epoch_time(created_at_dt.isoformat().replace("+00:00", "Z"))
        contact_id = str(record.get("contactId"))

        is_active = record.get("isActive")
        solved_at = None
        if not is_active:
            if last_update_time is not None:
                solved_at = utilities.get_epoch_time(last_update_time)

        first_responded_at = None
        first_response_time = record.get("agentStartDate")
        if first_response_time:
            first_responded_at = utilities.get_epoch_time(first_response_time)

        tags = []
        skill_id = record.get("skillId")
        if skill_id:
            tags.append(str(skill_id))
        skill_name = record.get("skillName")
        if skill_name:
            tags.append(skill_name)

        campaign_id = record.get("campaignId")
        queue = None
        if campaign_id is not None:
            queue = get_queue(campaign_id)

        conversation = {
            "created_at": created_at,
            "import_id": contact_id,
            "channel": channel,
            "status": status,
            "queue": queue,
            "first_responded_at": first_responded_at,
            "solved_at": solved_at,
            "assignee_import_id": assignee_import_id,
            "tags": tags
        }

        conversations.append(conversation)

    return conversations