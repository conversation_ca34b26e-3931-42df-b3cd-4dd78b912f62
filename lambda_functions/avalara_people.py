import nice_incontact
import utilities
from slack_util import send_slack_error
from logger_util import get_logger

logger = get_logger(__name__)


def get_agent_time_zone(time_zone):
    if time_zone == "(GMT-08:00) Pacific Time (US & Canada)":
        return "America/Los_Angeles"
    elif time_zone == "(GMT-07:00) Mountain Time (US & Canada)":
        return "America/Denver"
    elif time_zone == "(GMT-06:00) Central Time (US & Canada)":
        return "America/Chicago"
    elif time_zone == "(GMT-05:00) Eastern Time (US & Canada)":
        return "America/New_York"
    elif time_zone == "(UTC-06:00) Easter Island":
        return "Pacific/Easter"
    elif time_zone == "(GMT-03:00) Brasilia":
        return "America/Sao_Paulo"
    elif time_zone == "(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi":
        return "Asia/Kolkata"
    elif time_zone == "(GMT) Coordinated Universal Time":
        return "Europe/London"
    elif time_zone == "(GMT) Greenwich Mean Time : Dublin, Edinburgh, Lisbon, London":
        return "Europe/London"
    elif time_zone == "(GMT+01:00) Brussels, Copenhagen, Madrid, Paris":
        return "Europe/Paris"
    elif time_zone == "(GMT-03:00) Cayenne":
        return "America/Cayenne"
    elif time_zone == "(GMT-04:00) Santiago":
        return "America/Santiago"
    elif time_zone == "(GMT-04:00) Atlantic Time (Canada)":
        return "America/Halifax"
    elif time_zone == "(GMT+10:00) Canberra, Melbourne, Sydney":
        return "Australia/Sydney"
    elif time_zone == "America/Indiana/Indianapolis":
        return "America/Indiana/Indianapolis"

    return None


def get_agent_role(profile_name):
    if profile_name == "Avalara - Agent":
        return "a6b93913-9d68-401c-a8be-ffb791a456ac"   # Standard

    return None


def get_agent_channels():
    return ["phone"]


def get_agent_site(location):
    return "fef137d4-7538-4f3f-a2c0-a1accb42d37f"


def get_agent_teams(team_id):
    if team_id == 2449843:
        return ["68b7dd68-08ce-44d5-b895-121aa4964b92"]   # API User Creation

    return None


def get_agent_queues():
    return None


def get_agent_skills(team_id):
    if team_id == 2449843:
        return ["6f18db68-6888-4064-a888-6cb9f2dd3008"]   # Customer Support

    return None


def get_agent(agent_id):
    filter = f"/{agent_id}"

    # Fetch user from source
    source_agent_response = nice_incontact.get_agent(filter)

    # Check if the response is a dictionary with user details
    if not source_agent_response or not isinstance(source_agent_response, dict):
        error_msg = f"Unexpected or empty response for agent ID: {agent_id}. Response: {source_agent_response}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return {"error": f"Failed to retrieve agent {agent_id}"}

    return source_agent_response


def get_agent_skills(agent_id):
    filter = f"/{agent_id}"

    # Fetch user from source
    source_agent_skills_response = nice_incontact.get_agent_skills(filter)

    # Check if the response is a dictionary with user details
    if not source_agent_skills_response:
        error_msg = f"Unexpected or empty response for agent skills for agent ID: {agent_id}. Response: {source_agent_skills_response}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return {"error": f"Failed to retrieve agent skills for agent {agent_id}"}

    return source_agent_skills_response


def get_person(agent_id):
    # Fetch user from source
    source_agent_response = get_agent(agent_id)
    if source_agent_response.get("error"):
        error_msg = f"Unexpected or empty response for agent ID: {agent_id}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return {"error": source_agent_response["error"]}
    
    # Do not sync people/agent states/conversations if team is not Product Support
    agent_team_id = source_agent_response.get("teamId")
    if agent_team_id != 2449843:
        log_msg = f"Team ID [{agent_team_id}] is not Product Support - do not sync"
        logger.info(log_msg)
        return {"error": log_msg}

    # Role
    agent_profile_name = source_agent_response.get("profileName")
    role = get_agent_role(agent_profile_name)
    if role is None:
        log_msg = f"Agent Profile [{agent_profile_name}] has no role - do not sync"
        logger.info(log_msg)
        return {"error": log_msg}

    agent_email = source_agent_response.get("emailAddress")
    agent_first_name = source_agent_response.get("firstName")
    agent_last_name = source_agent_response.get("lastName")
    agent_name = f"{agent_first_name} {agent_last_name}"
    agent_time_zone = source_agent_response.get("timeZone")
    create_date = source_agent_response.get("createDate")
    agent_start_date = utilities.get_epoch_time(create_date)
    agent_end_date = None
    agent_active = True
    if not agent_active:
        agent_inactive_date = source_agent_response.get("inactiveDate")
        if agent_inactive_date:
            agent_end_date = utilities.get_epoch_time(agent_inactive_date)

    agent_hire_date = source_agent_response.get("hireDate")
    if agent_hire_date:
        agent_hire_date = utilities.get_epoch_time(agent_hire_date)

    agent_termination_date = source_agent_response.get("terminationDate")
    if agent_termination_date:
        agent_termination_date = utilities.get_epoch_time(agent_termination_date)

    if not agent_email or not agent_name:
        error_msg = f"Email or name missing for agent ID. Email: {agent_email}, Name: {agent_name}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return {"error": f"Failed to retrieve agent {agent_id}"}

    # Time Zone
    time_zone = get_agent_time_zone(agent_time_zone)

    # Staffable
    staffable = True

    # Channels
    channels = get_agent_channels()

    # Site
    location = source_agent_response.get("location")
    site = get_agent_site(location)

    # Teams
    teams = get_agent_teams(agent_team_id)

    # Queues
    queues = None

    # Skills
    source_agent_skills_response = get_agent_skills(agent_team_id)
    skills = source_agent_skills_response

    # Prepare Assembled data
    source_data = {
        "source_agent_active": agent_active,
        "source_agent_id": str(agent_id),
        "source_agent_email": agent_email,
        "source_agent_first_name": agent_first_name,
        "source_agent_last_name": agent_last_name,
        "source_agent_name": agent_name,
        "source_agent_start_date": agent_start_date,
        "source_agent_end_date": agent_end_date,
        "source_agent_role": role,
        "source_agent_channels": channels,
        "source_agent_queues": queues,
        "source_agent_teams": teams,
        "source_agent_site": site,
        "source_agent_time_zone": time_zone,
        "source_agent_staffable": staffable
    }

    return source_data