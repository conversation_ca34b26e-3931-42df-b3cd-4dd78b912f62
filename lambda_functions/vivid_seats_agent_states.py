import utilities
import assembled_agent_states
import vivid_seats_people as vivid_seats_people
from slack_util import send_slack_error
from logger_util import get_logger

logger = get_logger(__name__)

def get_state(agent_state_id):
    if agent_state_id == 0:
        return "LoggedOut"
    elif agent_state_id == 1:
        return "Available"
    elif agent_state_id == 2:
        return "Unavailable"
    elif agent_state_id == 3:
        return "InboundContact"
    elif agent_state_id == 4:
        return "OutboundContact"
    elif agent_state_id == 5:
        return "InboundConsult"
    elif agent_state_id == 6:
        return "OutboundConsult"
    elif agent_state_id == 7:
        return "Dialer"
    elif agent_state_id == 8:
        return "LoggedIn"

    return None


def update_presence_states(agent_presence_records):
    """
    Fetch and match agent presence records, check platform IDs, and update states in Assembled.
    """

    # Loop through agent presence records
    for agent_record in agent_presence_records:
        agent_id = agent_record.get("agentId")
        agent_state_id = agent_record.get("agentStateId")
        # agent_state_name = agent_record.get("agentStateName")
        agent_state_name = get_state(agent_state_id)

        logger.info(f"Processing sys_id: {agent_id}, State: {agent_state_name}")

        # Fetch user from source
        source_data = vivid_seats_people.get_person(agent_id)
        if source_data.get("error") is not None:
            error_msg = source_data["error"]
            logger.error(error_msg)
            # send_slack_error(error_msg)
            continue

        # If the agent role is not set, do not import the agent state
        if source_data.get("source_agent_role") is None:
            continue

        updated_at = agent_record.get("lastUpdateTime")
        epoch_time = utilities.get_epoch_time(updated_at)

        source_data["source_state"] = agent_state_name
        source_data["source_state_time"] = epoch_time

        # Check and update agent in Assembled
        result = assembled_agent_states.update_agent_state(source_data)
        logger.debug(result)