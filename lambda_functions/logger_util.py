import os
import logging

def get_logger(name):
    """
    Get a logger instance configured based on environment variables.
    
    Environment Variables:
    - LOG_LEVEL: The logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL). Defaults to INFO.
    - LOG_FORMAT: The log format. Defaults to a standard format with timestamp.
    
    Args:
        name (str): The name of the logger (typically __name__ of the calling module)
        
    Returns:
        logging.Logger: Configured logger instance
    """
    log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
    log_format = os.getenv('LOG_FORMAT', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Create logger
    logger = logging.getLogger(name)
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(log_format)
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    try:
        logger.setLevel(getattr(logging, log_level))
    except AttributeError:
        logger.setLevel(logging.INFO)
        logger.warning(f"Invalid LOG_LEVEL '{log_level}' specified. Defaulting to INFO.")
    
    return logger 