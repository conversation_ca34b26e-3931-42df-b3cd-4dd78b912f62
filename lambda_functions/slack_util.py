import json
import requests
from datetime import datetime, timezone
from logger_util import get_logger

logger = get_logger(__name__)

SLACK_WEBHOOK_URL = "*******************************************************************************"

def send_slack_error(error_message, context=None):
    try:
        utc_now = datetime.now(timezone.utc).isoformat().replace("+00:00", "Z")
        message = {
            "text": f"🚨 {utc_now}\n\n{error_message}"
        }
        
        response = requests.post(SLACK_WEBHOOK_URL, json=message)
        response.raise_for_status()
        logger.info("Successfully sent error notification to <PERSON>lack")
    except Exception as e:
        logger.error(f"Failed to send Slack notification: {str(e)}") 