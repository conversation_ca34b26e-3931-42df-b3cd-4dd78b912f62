import requests
import nice_incontact
import avalara_agent_states
from datetime import datetime, timedelta
from slack_util import send_slack_error
from logger_util import get_logger

logger = get_logger(__name__)

def handler(event, context):
    try:
        logger.info("Starting handler execution")
        logger.info("Fetching NICE inContact API token...")
        token_response = nice_incontact.get_token()
        if token_response is None:
            error_msg = "Failed to obtain token"
            logger.error(error_msg)
            # send_slack_error(error_msg)
            return {
                "statusCode": 400,
                "body": "Failed to obtain token"
            }

        # Get agent states
        last_run = (datetime.now() - timedelta(seconds=15)).strftime("%Y-%m-%d %H:%M:%S")
        logger.info(f"Fetching agent states since: {last_run}")
        agent_states = nice_incontact.get_agent_states(last_run)
        logger.info(f"Retrieved {len(agent_states) if agent_states else 0} agent states")

        logger.info("Updating presence states...")
        update_result = avalara_agent_states.update_presence_states(agent_states)
        logger.info("Update complete")

        return {
            "statusCode": 200,
            "body": {
                "message": "Agent states updated successfully",
                "result": update_result
            }
        }

    except requests.exceptions.RequestException as e:
        error_msg = f"API request error: {str(e)}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        return {
            "statusCode": 500,
            "body": f"An error occurred with the API request: {str(e)}"
        }
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        logger.error(error_msg)
        # send_slack_error(error_msg)
        raise e