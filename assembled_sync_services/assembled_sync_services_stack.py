from aws_cdk import (
    aws_lambda as _lambda,
    aws_events as events,
    aws_events_targets as targets,
    Duration,
    Stack,
    BundlingOptions,
    aws_secretsmanager as secretsmanager,
    aws_dynamodb as dynamodb,
    RemovalPolicy,
)
from constructs import Construct


class AssembledSyncServicesStack(Stack):
    def __init__(self, scope: Construct, construct_id: str, **kwargs) -> None:
        super().__init__(scope, construct_id, **kwargs)

        assembled_secret = secretsmanager.Secret.from_secret_name_v2(
            self, "AssembledSecrets", "AssembledSecrets"
        )

        assembled_people_table = dynamodb.Table(
            self, "DKAssembledPeopleCache",
            partition_key=dynamodb.Attribute(
                name="email",
                type=dynamodb.AttributeType.STRING
            ),
            billing_mode=dynamodb.BillingMode.PAY_PER_REQUEST,
            removal_policy=RemovalPolicy.DESTROY,
            point_in_time_recovery=True,
        )

        draft_kings_agents_table = dynamodb.Table(
            self, "DraftKingsAgentsCache",
            partition_key=dynamodb.Attribute(
                name="agent_id",
                type=dynamodb.AttributeType.STRING
            ),
            billing_mode=dynamodb.BillingMode.PAY_PER_REQUEST,
            removal_policy=RemovalPolicy.DESTROY,
            point_in_time_recovery=True,
        )

        agent_states_lambda = _lambda.Function(
            self, "DKAgentStatesLambda",
            runtime=_lambda.Runtime.PYTHON_3_12,
            handler="agent_states_handler.handler",
            code=_lambda.Code.from_asset(
                path='lambda_functions/',
                bundling=BundlingOptions(
                    image=_lambda.Runtime.PYTHON_3_12.bundling_image,
                    command=[
                        "bash", "-c",
                        "pip install -r requirements.txt -t /asset-output && cp -au . /asset-output"
                    ],
                )
            ),
            timeout=Duration.seconds(900),
            memory_size=512,
            environment={
                "ASSEMBLED_PEOPLE_TABLE": assembled_people_table.table_name,
                "DRAFT_KINGS_AGENTS_TABLE": draft_kings_agents_table.table_name
            }
        )

        assembled_secret.grant_read(agent_states_lambda)

        agent_states_rule = events.Rule(
            self, "AgentStatesScheduleRule",
            schedule=events.Schedule.rate(Duration.minutes(1))
        )

        agent_states_rule.add_target(
            targets.LambdaFunction(agent_states_lambda))

        conversations_lambda = _lambda.Function(
            self, "DKConversationsLambda",
            runtime=_lambda.Runtime.PYTHON_3_12,
            handler="conversations_handler.handler",
            code=_lambda.Code.from_asset(
                path='lambda_functions/',
                bundling=BundlingOptions(
                    image=_lambda.Runtime.PYTHON_3_12.bundling_image,
                    command=[
                        "bash", "-c",
                        "pip install -r requirements.txt -t /asset-output && cp -au . /asset-output"
                    ],
                )
            ),
            timeout=Duration.seconds(300),
            memory_size=512,
            environment={
                "ASSEMBLED_PEOPLE_TABLE": assembled_people_table.table_name,
                "DRAFT_KINGS_AGENTS_TABLE": draft_kings_agents_table.table_name
            }
        )

        assembled_secret.grant_read(conversations_lambda)

        conversations_rule = events.Rule(
            self, "ConversationsScheduleRule",
            schedule=events.Schedule.rate(Duration.minutes(15))
        )
        conversations_rule.add_target(
            targets.LambdaFunction(conversations_lambda))
        
        assembled_people_table.grant_read_write_data(agent_states_lambda)
        assembled_people_table.grant_read_write_data(conversations_lambda)
        draft_kings_agents_table.grant_read_write_data(agent_states_lambda)
        draft_kings_agents_table.grant_read_write_data(conversations_lambda)