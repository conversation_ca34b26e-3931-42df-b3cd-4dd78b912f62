from aws_cdk import (
    aws_lambda as _lambda,
    aws_events as events,
    aws_events_targets as targets,
    Duration,
    Stack,
    BundlingOptions,
    aws_secretsmanager as secretsmanager,
    aws_dynamodb as dynamodb,
    RemovalPolicy,
)
from constructs import Construct


class VividSeatsSyncServicesStack(Stack):
    def __init__(self, scope: Construct, construct_id: str, **kwargs) -> None:
        super().__init__(scope, construct_id, **kwargs)

        assembled_secret = secretsmanager.Secret.from_secret_name_v2(
            self, "VividSeatsAssembledSecrets", "VividSeatsAssembledSecrets"
        )

        assembled_people_table = dynamodb.Table(
            self, "VividAssembledPeopleCache",
            partition_key=dynamodb.Attribute(
                name="email",
                type=dynamodb.AttributeType.STRING
            ),
            billing_mode=dynamodb.BillingMode.PAY_PER_REQUEST,
            removal_policy=RemovalPolicy.DESTROY,
            point_in_time_recovery=True,
        )
        
        client_agent_cache_table = dynamodb.Table(
            self, "VividClientAgentCache",
            partition_key=dynamodb.Attribute(
                name="agent_id",
                type=dynamodb.AttributeType.STRING
            ),
            billing_mode=dynamodb.BillingMode.PAY_PER_REQUEST,
            removal_policy=RemovalPolicy.DESTROY,
            point_in_time_recovery=True,
        )

        vivid_seats_agent_states_lambda = _lambda.Function(
            self, "VividSeatsAgentStatesLambda",
            runtime=_lambda.Runtime.PYTHON_3_12,
            handler="vivid_seats_agent_states_handler.handler",
            code=_lambda.Code.from_asset(
                path='lambda_functions/',
                bundling=BundlingOptions(
                    image=_lambda.Runtime.PYTHON_3_12.bundling_image,
                    command=[
                        "bash", "-c",
                        "pip install -r requirements.txt -t /asset-output && cp -au . /asset-output"
                    ],
                )
            ),
            timeout=Duration.seconds(300),
            memory_size=512,
            environment={
                "ASSEMBLED_PEOPLE_TABLE": assembled_people_table.table_name,
                "DRAFT_KINGS_AGENTS_TABLE": client_agent_cache_table.table_name,
                "VIVID_SEATS_AGENTS_TABLE": client_agent_cache_table.table_name
            }
        )

        assembled_secret.grant_read(vivid_seats_agent_states_lambda)
        assembled_people_table.grant_read_write_data(vivid_seats_agent_states_lambda)
        client_agent_cache_table.grant_read_write_data(vivid_seats_agent_states_lambda)
        
        vivid_seats_agent_states_rule = events.Rule(
            self, "VividSeatsAgentStatesScheduleRule",
            schedule=events.Schedule.rate(Duration.minutes(1))
        )

        vivid_seats_agent_states_rule.add_target(
            targets.LambdaFunction(vivid_seats_agent_states_lambda))

        vivid_seats_conversations_lambda = _lambda.Function(
            self, "VividSeatsConversationsLambda",
            runtime=_lambda.Runtime.PYTHON_3_12,
            handler="vivid_seats_conversations_handler.handler",
            code=_lambda.Code.from_asset(
                path='lambda_functions/',
                bundling=BundlingOptions(
                    image=_lambda.Runtime.PYTHON_3_12.bundling_image,
                    command=[
                        "bash", "-c",
                        "pip install -r requirements.txt -t /asset-output && cp -au . /asset-output"
                    ],
                )
            ),
            timeout=Duration.seconds(300),
            memory_size=512,
            environment={
                "ASSEMBLED_PEOPLE_TABLE": assembled_people_table.table_name,
                "DRAFT_KINGS_AGENTS_TABLE": client_agent_cache_table.table_name,
                "VIVID_SEATS_AGENTS_TABLE": client_agent_cache_table.table_name
            }
        )

        assembled_secret.grant_read(vivid_seats_conversations_lambda)
        assembled_people_table.grant_read_write_data(vivid_seats_conversations_lambda)
        client_agent_cache_table.grant_read_write_data(vivid_seats_conversations_lambda)

        vivid_seats_conversations_rule = events.Rule(
            self, "VividSeatsConversationsScheduleRule",
            schedule=events.Schedule.rate(Duration.minutes(15))
        )

        vivid_seats_conversations_rule.add_target(
            targets.LambdaFunction(vivid_seats_conversations_lambda)) 