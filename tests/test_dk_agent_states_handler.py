import sys
import os
import json
import dotenv

# Load environment variables from .env file in project root
dotenv.load_dotenv()

# Add the lambda directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '../lambda_functions'))

from agent_states_handler import handler

if __name__ == "__main__":
    # Dummy event and context
    fake_event = {}
    fake_context = {}

    result = handler(fake_event, fake_context)
    print(json.dumps(result, indent=2))
 