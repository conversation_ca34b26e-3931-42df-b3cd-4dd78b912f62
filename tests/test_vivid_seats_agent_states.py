import json
from datetime import datetime
import sys
import os

from vivid_seats_agent_states_handler import handler
from logger_util import get_logger

logger = get_logger(__name__)

def test_handler():
    """
    Test the Vivid Seats agent states handler
    """
    try:
        logger.info(f"Starting handler test at {datetime.now()}")
        result = handler(None, None)
        
        logger.info("Handler Response:")
        logger.info(json.dumps(result, indent=2))
        
        return result
    except Exception as e:
        logger.error(f"Error occurred: {str(e)}")
        raise

if __name__ == "__main__":
    test_handler() 