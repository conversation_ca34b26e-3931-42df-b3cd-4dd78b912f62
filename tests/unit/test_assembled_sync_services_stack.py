import aws_cdk as core
import aws_cdk.assertions as assertions

from assembled_sync_services.assembled_sync_services_stack import AssembledSyncServicesStack

# example tests. To run these tests, uncomment this file along with the example
# resource in assembled_sync_services/assembled_sync_services_stack.py
def test_sqs_queue_created():
    app = core.App()
    stack = AssembledSyncServicesStack(app, "assembled-sync-services")
    template = assertions.Template.from_stack(stack)

#     template.has_resource_properties("AWS::SQS::Queue", {
#         "VisibilityTimeout": 300
#     })
