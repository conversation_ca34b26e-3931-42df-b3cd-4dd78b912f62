# Welcome to your CDK Python project!

This is a blank project for CDK development with Python.

The `cdk.json` file tells the CDK Toolkit how to execute your app.

## Setting Up AWS SSO Login

To authenticate using AWS SSO for the `envoy` profile, follow these steps:

### 1. Configure AWS SSO Profile
Ensure that your AWS SSO configuration file (`~/.aws/config`) includes the following settings:

```
[profile envoy]
sso_session = envoy
sso_account_id = ************
sso_role_name = AdministratorAccess
region = us-east-1
output = json

[sso-session envoy]
sso_start_url = https://d-9067fff7dc.awsapps.com/start/#
sso_region = us-east-1
sso_registration_scopes = sso:account:access
```

### 2. Login to AWS SSO
Before using AWS CLI or CDK, you must log in using AWS SSO:

```
$ aws sso login --profile envoy
```
This will open a browser window prompting you to authenticate.

### 3. Verify Credentials
To confirm that your profile is correctly authenticated, run:

```
$ aws sts get-caller-identity --profile envoy
```
This should return information about your authenticated AWS identity.

### 4. Install AWS CDK
Once authentication is complete, install the AWS CDK globally:

```
$ npm install -g aws-cdk
```

Verify the installation:

```
$ cdk --version
```

---

## Project Setup

This project is set up like a standard Python project. The initialization
process also creates a virtualenv within this project, stored under the `.venv`
directory. To create the virtualenv, it assumes that there is a `python3`
(or `python` for Windows) executable in your path with access to the `venv`
package. If for any reason the automatic creation of the virtualenv fails,
you can create the virtualenv manually.

### Manually Create a Virtual Environment

On macOS and Linux:

```
$ python3 -m venv .venv
```

After the init process completes and the virtualenv is created, activate it using:

```
$ source .venv/bin/activate
```

On Windows:

```
% .venv\Scripts\activate.bat
```

### Install Dependencies
Once the virtualenv is activated, install the required dependencies:

```
$ pip install -r requirements.txt
```

At this point, you can now synthesize the CloudFormation template for this code:

```
$ cdk synth
```

To add additional dependencies, for example, other CDK libraries, just add
them to your `setup.py` file and rerun the `pip install -r requirements.txt`
command.

## Useful Commands

* `aws sso login --profile envoy`  log in to AWS SSO before running CDK commands
* `cdk ls`          list all stacks in the app
* `cdk synth`       emits the synthesized CloudFormation template
* `cdk deploy`      deploy this stack to your default AWS account/region
* `cdk diff`        compare deployed stack with current state
* `cdk docs`        open CDK documentation

## How to run project locally 
# 1. VS Code Extensions
If you're using VS Code, install the following extensions: 
- Python
- Python Debugger (debugpy)

Ensure VS Code uses the correct Python interpreter. It should match your `.venv` version (you'll be prompted if it's not configured correctly).
How to select: 
![alt text](image.png)

# 2. Create .env file 
Here's a template:
``` 
LOCAL_DEVELOPMENT=1
ASSEMBLED_API_BASE_URL=https://api.assembledhq.com
ASSEMBLED_API_KEY={{assembled_api_key}}
SNOW_SUBDOMAIN={{snow_subdomain}}
SNOW_API_KEY={{snow_api_key}}
SNOW_CLIENT_ID={{snow_client_id}}
SNOW_CLIENT_SECRET={{snow_client_secret}}
[CLIENT_NAME]_ASSEMBLED_API_KEY={{client_name_assembled_api_key}}
ASSEMBLED_PEOPLE_TABLE={{dynamodb_table_name}}
[CLIENT_NAME]_AGENTS_TABLE={{client_name_table_name}}
[CLIENT_NAME]_AGENTS_TABLE={{client_name_table_name}}
...or what other values you'll need to get the script running
```
* Replace the placeholders with actual credentials

# 3. Add a Test Script 
Place your test file in the `/tests` directory. Example: 
    - tests/test_dk_conversations_handler.py

## 4. Configure Debugger (launch.json)
In your `.vscode/launch.json`: 

```
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python Debugger: Current File",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/tests/[name_of_test_script].py",
      "console": "integratedTerminal",
      "env": {
        "AWS_PROFILE": "envoy"
      }
    }
  ]
}
```
* Replace `[name_of_test_script]` with your actual script name

## 5. Run and Debug
Open the test script, click **Run and Debug** and select your configured debugger:
![alt text](image-1.png)

Enjoy!