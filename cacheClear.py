import boto3

session = boto3.Session(profile_name='envoy')
dynamodb = session.resource('dynamodb')

table = dynamodb.Table('AssembledSyncServicesStack-DraftKingsAgentsCache41122FDF-1WYWKJWXYAW0F')

# Replace these with your actual primary key attribute names
PARTITION_KEY = 'agent_id'
SORT_KEY = None# If your table has no sort key, ignore this line

def clear_table(table):
    key_names = [PARTITION_KEY]
    if SORT_KEY:
        key_names.append(SORT_KEY)

    projection_expr = ', '.join(f'#{k}' for k in key_names)
    expr_attr_names = {f'#{k}': k for k in key_names}

    scan_kwargs = {
        'ProjectionExpression': projection_expr,
        'ExpressionAttributeNames': expr_attr_names
    }

    done = False
    start_key = None

    while not done:
        if start_key:
            scan_kwargs['ExclusiveStartKey'] = start_key

        response = table.scan(**scan_kwargs)
        items = response['Items']

        with table.batch_writer() as batch:
            for item in items:
                key_to_delete = {k: item[k] for k in key_names}
                batch.delete_item(Key=key_to_delete)

        start_key = response.get('LastEvaluatedKey', None)
        done = start_key is None

clear_table(table)
